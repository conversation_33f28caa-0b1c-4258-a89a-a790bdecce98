# api/v1/start_node.py

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import Dict, Any
import logging
import uuid
from datetime import datetime, timezone
import asyncio

from database.db import get_db
from utils.auth import get_current_user
from schemas.workflow_run_nodes import WorkflowRunNodeCreate, WorkflowRunNodeUpdate
from schemas.workflow_runs import WorkflowRunUpdate
from service.workflow_run_nodes_service import create_workflow_run_node, update_workflow_run_node
from service.workflow_runs_service import update_workflow_run

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/startNode")
async def start_node(
    request_data: Dict[str, Any],
    req: Request,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """
    开始节点API

    Args:
        request_data (Dict[str, Any]): 请求数据
        req (Request): 请求对象
        db (Session): 数据库会话
        current_user_id (str): 当前用户ID

    Returns:
        Dict: 响应数据
    """
    # 生成节点运行ID
    node_run_id = str(uuid.uuid4())
    node_id = request_data.get("nodeId", "unknown")
    workflow_run_id = request_data.get("workflowRunId", "unknown")

    # 调用开始时登录 workflow_run_nodes 表，状态为 RUNNING
    try:
        node_run_data = WorkflowRunNodeCreate(
            user_id=current_user_id,
            workflow_run_id=workflow_run_id,
            node_id=node_id,
            node_ver="1.0",
            status="RUNNING",
            params=str(request_data),
            memory="",
            flow_json=str(request_data),
            output_type="00",
            output="",
            ip_addr=req.client.host
        )

        # 创建节点运行记录
        created_node = create_workflow_run_node(db, node_run_data)
        node_run_id = created_node.id  # 使用数据库生成的ID
        logger.info(f"Start node execution started for node {node_id}, run_id: {node_run_id}")

    except Exception as e:
        logger.error(f"Failed to create workflow run node record: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initialize node execution: {str(e)}"
        )

    try:
        logger.info(f"Start node called by user {current_user_id}")
        logger.info(f"Request data: {request_data}")

        # TODO: 实现开始节点的具体逻辑
        await asyncio.sleep(3)  # 実際の処理に置き換え


        # 执行成功，更新状态为 SUCCESS
        update_data = WorkflowRunNodeUpdate(
            status="SUCCESS",
            output_type="01",
            output=str({
                "status": "success",
                "message": "Start node executed successfully",
                "node_type": "start"
            }),
            finished_at=datetime.now(timezone.utc)
        )
        update_workflow_run_node(db, node_run_id, update_data)
        logger.info(f"Start node execution completed successfully for node {node_id}")

        return {
            "status": "success",
            "message": "Start node executed successfully",
            "node_type": "start",
            "user_id": current_user_id,
            "node_run_id": node_run_id,
            "data": request_data
        }

    except Exception as e:
        logger.error(f"Error in start node: {e}")

        # 执行失败，更新节点状态为 FAILED
        try:
            update_data = WorkflowRunNodeUpdate(
                status="FAILED",
                output_type="02",
                output=str({"error": str(e)}),
                finished_at=datetime.now(timezone.utc)
            )
            update_workflow_run_node(db, node_run_id, update_data)
            logger.info(f"Start node execution failed for node {node_id}, error recorded")

            # 任何节点失败，更新整个工作流状态为 FAILED
            try:
                workflow_update_data = WorkflowRunUpdate(
                    status="FAILED",
                    finished_at=datetime.now(timezone.utc)
                )
                update_workflow_run(db, workflow_run_id, workflow_update_data)
                logger.info(f"Workflow {workflow_run_id} marked as FAILED due to start node failure")
            except Exception as workflow_update_error:
                logger.error(f"Failed to update workflow status to FAILED: {workflow_update_error}")

        except Exception as update_error:
            logger.error(f"Failed to update node status to FAILED: {update_error}")

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Start node execution failed: {str(e)}"
        )
