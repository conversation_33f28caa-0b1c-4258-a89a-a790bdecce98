FROM python:3.11

# 環境変数
ENV HTTP_PORT=8000

# ポートバインド
EXPOSE $HTTP_PORT

# 作業ディレクトリを設定
WORKDIR /app/src

# FFmpegとLibreOfficeのインストール
RUN apt update && apt install -y ffmpeg && apt install -y graphviz && apt install -y libreoffice

# requirements.txtをコピーしてインストール
#COPY ./src/rag-api/requirements.txt .

# アプリケーションファイルをコピー
COPY ./src/afireai_api/ .

# 依存関係のインストール
RUN pip install --upgrade pip

RUN pip uninstall websocket-client

# 非同期処理に必要なライブラリをインストール
RUN pip install websockets

RUN pip install --no-cache-dir -r requirements.txt

# FastAPIアプリケーションを起動するためのコマンド
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--reload-exclude", "*.git*"]
#CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
#ENTRYPOINT ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
