# main.py
import logging
import asyncio
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from utils.auth import verify_token

from routers import user, api_keys, apps, workflow_runs, workflow_run_nodes, variables, run, workflows, knowledge, knowledge_files, search_similar_images
from api.v1.router import v1_router
from database.db import engine, Base
# モデルのインポート（テーブル作成のため）
from models.knowledge_chunks import KnowledgeChunk
# ログの設定
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("fastapi-logger")

app = FastAPI()

# CORS の設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# データベーステーブルの初期化
Base.metadata.create_all(bind=engine)

# ルーターの登録
app.include_router(user.router, prefix="/users", tags=["Users"])
# app.include_router(upload_file.router, prefix="/files", tags=["Upload Files"], dependencies=[Depends(verify_token)])
app.include_router(api_keys.router, prefix="/api-keys", tags=["API Keys"])
app.include_router(apps.router, prefix="/apps", tags=["Apps"])
app.include_router(workflow_runs.router, prefix="/workflow-runs", tags=["Workflow Runs"])
app.include_router(workflow_run_nodes.router, prefix="/workflow-run-nodes", tags=["Workflow Run Nodes"])
app.include_router(variables.router, prefix="/variables", tags=["Variables"])
app.include_router(run.router, prefix="/run", tags=["Run"])
app.include_router(v1_router, prefix="/run/v1", tags=["Run V1 Nodes"])

# 知識ベース
app.include_router(knowledge.router, prefix="/knowledge", tags=["Knowledge"])
app.include_router(knowledge_files.router, prefix="/knowledge-files", tags=["KnowledgeFiles"])

#API 実行
app.include_router(workflows.router, prefix="/v1/workflows", tags=["api Workflow Run Nodes"])

# 画像検索
app.include_router(search_similar_images.router, prefix="/search-similar-images", tags=["api Search Similar Images"])

@app.on_event("startup")
# async def startup_event():
    # meeting summary のタスクを起動(15秒ごとに実行)
    # asyncio.create_task(monitor_task_meeting_summaries())
@app.get("/")
async def root():
    return {"message": "Welcome to ifrieai app"}

@app.get("/health")
async def health_check():
    return {"status": "ok"}
