#!/usr/bin/env python3
"""
Migration script to add valid_flg column to knowledge_files table
Run this script to update the database schema for existing installations
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError

# Add the parent directory to the path to import database configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def get_database_url():
    """Get database URL from environment variables"""
    db_host = os.getenv("__DB_HOST", "localhost")
    db_port = os.getenv("__DB_PORT", "3306")
    db_user = os.getenv("__DB_USER", "root")
    db_password = os.getenv("__DB_PASSWORD", "")
    db_name = os.getenv("__DB_NAME", "afireai_workflow")
    
    return f"mysql+pymysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

def run_migration():
    """Execute the migration to add valid_flg column"""
    try:
        # Create database engine
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        print("Starting migration: Adding valid_flg column to knowledge_files table...")
        
        with engine.connect() as connection:
            # Check if column already exists
            result = connection.execute(text("""
                SELECT COUNT(*) as count 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'knowledge_files' 
                AND COLUMN_NAME = 'valid_flg'
            """))
            
            column_exists = result.fetchone()[0] > 0
            
            if column_exists:
                print("✓ Column 'valid_flg' already exists in knowledge_files table")
                return True
            
            # Add the column
            print("Adding valid_flg column...")
            connection.execute(text("""
                ALTER TABLE knowledge_files 
                ADD COLUMN valid_flg VARCHAR(1) DEFAULT '1' COMMENT '有効区分: 1=有効, 0=無効'
            """))
            
            # Update existing records
            print("Updating existing records...")
            result = connection.execute(text("""
                UPDATE knowledge_files 
                SET valid_flg = '1' 
                WHERE valid_flg IS NULL
            """))
            
            print(f"Updated {result.rowcount} existing records")
            
            # Add indexes for better performance
            print("Adding indexes...")
            try:
                connection.execute(text("""
                    CREATE INDEX idx_knowledge_files_valid_flg ON knowledge_files(valid_flg)
                """))
                print("✓ Created index: idx_knowledge_files_valid_flg")
            except OperationalError as e:
                if "Duplicate key name" in str(e):
                    print("✓ Index idx_knowledge_files_valid_flg already exists")
                else:
                    raise
            
            try:
                connection.execute(text("""
                    CREATE INDEX idx_knowledge_files_kb_id_valid_flg ON knowledge_files(kb_id, valid_flg)
                """))
                print("✓ Created index: idx_knowledge_files_kb_id_valid_flg")
            except OperationalError as e:
                if "Duplicate key name" in str(e):
                    print("✓ Index idx_knowledge_files_kb_id_valid_flg already exists")
                else:
                    raise
            
            # Verify migration
            result = connection.execute(text("""
                SELECT COUNT(*) as total_files, 
                       SUM(CASE WHEN valid_flg = '1' THEN 1 ELSE 0 END) as valid_files,
                       SUM(CASE WHEN valid_flg = '0' THEN 1 ELSE 0 END) as invalid_files
                FROM knowledge_files
            """))
            
            stats = result.fetchone()
            print(f"\nMigration completed successfully!")
            print(f"Total files: {stats[0]}")
            print(f"Valid files: {stats[1]}")
            print(f"Invalid files: {stats[2]}")
            
            # Commit the transaction
            connection.commit()
            
        return True
        
    except Exception as e:
        print(f"Migration failed: {e}")
        return False

if __name__ == "__main__":
    print("Knowledge Files Migration Script")
    print("=" * 40)
    
    success = run_migration()
    
    if success:
        print("\n✓ Migration completed successfully!")
        sys.exit(0)
    else:
        print("\n✗ Migration failed!")
        sys.exit(1)
