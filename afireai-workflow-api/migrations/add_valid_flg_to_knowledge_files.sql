-- Migration script to add valid_flg column to knowledge_files table
-- Execute this script to update existing database schema

-- Add valid_flg column to knowledge_files table
ALTER TABLE knowledge_files 
ADD COLUMN valid_flg VARCHAR(1) DEFAULT '1' COMMENT '有効区分: 1=有効, 0=無効';

-- Update existing records to set valid_flg = '1' (valid)
UPDATE knowledge_files 
SET valid_flg = '1' 
WHERE valid_flg IS NULL;

-- Add index for better query performance
CREATE INDEX idx_knowledge_files_valid_flg ON knowledge_files(valid_flg);
CREATE INDEX idx_knowledge_files_kb_id_valid_flg ON knowledge_files(kb_id, valid_flg);

-- Verify the migration
SELECT COUNT(*) as total_files, 
       SUM(CASE WHEN valid_flg = '1' THEN 1 ELSE 0 END) as valid_files,
       SUM(CASE WHEN valid_flg = '0' THEN 1 ELSE 0 END) as invalid_files
FROM knowledge_files;
