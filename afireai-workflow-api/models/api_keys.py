from sqlalchemy import Column, String, DateTime
from database.db import Base
from datetime import datetime

class APIKey(Base):
    __tablename__ = "api_keys"

    id = Column(String(36), primary_key=True, nullable=False)  # ID
    user_id = Column(String(36), nullable=False)  # ユーザーID
    app_id = Column(String(36), nullable=False)  # アプリID
    api_name = Column(String(255), nullable=True)  # API名称
    api_key = Column(String(128), nullable=False, unique=True)  # APIキー
    expiration = Column(DateTime, nullable=True)  # 有効期限
    memo = Column(String(255), nullable=True)  # メモ
    ip_addr = Column(String(20), nullable=True)  # IPアドレス
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)  # 作成タイムスタンプ
    updated_at = Column(DateTime, default=datetime.utcnow, nullable=False, onupdate=datetime.utcnow)  # 更新タイムスタンプ