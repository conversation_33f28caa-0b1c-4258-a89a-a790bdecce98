from sqlalchemy import Column, String, LargeBinary, DateTime
from database.db import Base
import datetime as dt

class AppIcon(Base):
    __tablename__ = "app_icons"
    app_id = Column(String(36), primary_key=True)
    user_id = Column(String(36), nullable=False)
    icon_data = Column(LargeBinary, nullable=False)  # MEDIUMBLOB
    mime_type = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=dt.datetime.utcnow)
    updated_at = Column(DateTime, default=dt.datetime.utcnow,
                        onupdate=dt.datetime.utcnow)