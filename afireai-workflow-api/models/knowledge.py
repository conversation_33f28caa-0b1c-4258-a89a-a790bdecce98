import uuid, datetime as dt
from sqlalchemy import Column, String, Integer, Float, DateTime
from database.db import Base

class Knowledge(Base):
    __tablename__ = "knowledge"

    kb_id           = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id         = Column(String(36), nullable=False)
    kb_name         = Column(String(36), nullable=False)
    kb_description  = Column(String(255))
    embedding_model = Column(String(255), default="openai://text-embedding-3-small")
    chunk_size      = Column(Integer, default=512)
    chunk_overlap   = Column(Integer, default=64)
    chunk_identifier= Column(String(255), default="\n\n")
    top_k           = Column(Integer, default=3)
    score_val       = Column(Float(2,1), default=0.5)
    ip_addr         = Column(String(20))
    created_at      = Column(DateTime, default=dt.datetime.utcnow)
    updated_at      = Column(DateTime, default=dt.datetime.utcnow, onupdate=dt.datetime.utcnow) 