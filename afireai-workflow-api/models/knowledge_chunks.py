import uuid
import datetime as dt
from sqlalchemy import Column, String, Text, Integer, DateTime, ForeignKey, LargeBinary
from database.db import Base

class KnowledgeChunk(Base):
    __tablename__ = "knowledge_chunks"

    chunk_id      = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    kb_id         = Column(String(36), ForeignKey("knowledge.kb_id"), nullable=False)
    file_id       = Column(String(36), ForeignKey("knowledge_files.file_id"), nullable=False)
    user_id       = Column(String(36), nullable=False)
    chunk_text    = Column(Text, nullable=False)
    chunk_index   = Column(Integer, nullable=False)  # ファイル内でのチャンクの順序
    token_count   = Column(Integer)  # トークン数
    embedding_vector = Column(LargeBinary)  # 埋め込みベクトル（MEDIUMBLOB）
    vector_id     = Column(String(36))  # Vector DBでのID
    chunk_metadata = Column(Text)  # JSON形式のメタデータ
    ip_addr       = Column(String(20))  # IPアドレス
    created_at    = Column(DateTime, default=dt.datetime.utcnow)
    updated_at    = Column(DateTime, default=dt.datetime.utcnow, onupdate=dt.datetime.utcnow)