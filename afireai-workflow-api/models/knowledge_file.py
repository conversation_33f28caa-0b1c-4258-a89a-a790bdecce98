import uuid, datetime as dt
from sqlalchemy import Column, String, BigInteger, DateTime, ForeignKey
from database.db import Base

class KnowledgeFile(Base):
    __tablename__ = "knowledge_files"

    file_id       = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    kb_id         = Column(String(36), ForeignKey("knowledge.kb_id"), nullable=False)
    user_id       = Column(String(36), nullable=False)
    file_name     = Column(String(255), nullable=False)
    file_type     = Column(String(100))
    file_size     = Column(BigInteger)
    storage_path  = Column(String(255))
    status        = Column(String(20), default="READY")  # READY/INGESTING/DONE/ERROR
    chunk_identifier = Column(String(255))
    uploaded_by   = Column(String(36))
    ip_addr       = Column(String(20))
    valid_flg     = Column(String(1), default="1")  # 有効区分: "1"=有効, "0"=無効
    created_at    = Column(DateTime, default=dt.datetime.utcnow)
    updated_at    = Column(DateTime, default=dt.datetime.utcnow, onupdate=dt.datetime.utcnow)