# models/token.py

from sqlalchemy import Column, String, DateTime
from database.db import Base
from datetime import datetime

class Tokens(Base):
    __tablename__ = "tokens"

    user_id = Column(String(36), primary_key=True, nullable=False)  # ユーザーID
    access_token = Column(String(255), nullable=False)  # Access Token
    refresh_token = Column(String(255), nullable=False)  # Refresh Token
    access_token_expiration = Column(DateTime, nullable=False)  # Access Tokenの有効期限
    refresh_token_expiration = Column(DateTime, nullable=False)  # Refresh Tokenの有効期限
    ip_addr = Column(String(20), nullable=True)  # IP
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, nullable=False, onupdate=datetime.utcnow)
