# models/workflow_runs.py

from sqlalchemy import Column, String, Text, DateTime
from database.db import Base
from datetime import datetime, timezone

class WorkflowRun(Base):
    __tablename__ = "workflow_runs"
    user_id = Column(String(36), nullable=False)  # ユーザーID
    app_id = Column(String(36), nullable=False)  # アプリID
    workflow_run_id = Column(String(36), primary_key=True, nullable=False)  # work flow run id
    type = Column(String(20), default="WEB")  # 数据库默认值同步
    status = Column(String(255))  # 実行状態
    params = Column(String(255))  # パラメーター
    memory = Column(String(50))  # メモリ
    flow_json = Column(Text, nullable=False)  # JSON
    started_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)  # 開始時刻
    finished_at = Column(DateTime, nullable=True)  # 終了時刻
    ip_addr = Column(String(20), nullable=False)  # IPアドレス
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)  # 作成タイムスタンプ
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False, onupdate=lambda: datetime.now(timezone.utc))  # 更新タイムスタンプ
