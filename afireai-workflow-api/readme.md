
1.	必要なパッケージをインストールします。

pip install -r requirements.txt

2.	サーバーを起動します。
uvicorn main:app --reload
python3.13 -m uvicorn main:app --reload


python3.13 -m uvicorn main:app --reload --reload-exclude *.git*




source venv/bin/activate
python3.13 -m uvicorn main:app --reload




## 启动API服务

请使用以下命令启动API服务：

```bash
cd /Users/<USER>/git/afireai-workflow/afireai-workflow-api/
source torch-env/bin/activate
python3.11 -m uvicorn main:app --reload
```

### 步骤说明：
1. **切换到项目目录**：`cd /Users/<USER>/git/afireai-workflow/afireai-workflow-api/`
2. **激活虚拟环境**：`source torch-env/bin/activate`
3. **启动服务**：`python3.11 -m uvicorn main:app --reload`

启动成功后，API服务将运行在 http://127.0.0.1:8000


