# routers/api_keys.py

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List
import uuid

from database.db import get_db
from models.api_keys import APIKey
from schemas.api_keys import APIKeyCreate, APIKeyUpdate, APIKeyResponse
from utils.auth import generate_api_key, get_current_user
from service.apps_service import (
    create_app, 
    update_app, 
    get_app, 
    get_apps_by_user, 
    delete_app
)

router = APIRouter()

@router.post("/create", response_model=APIKeyResponse, status_code=status.HTTP_201_CREATED)
async def create_api_key(
    request: APIKeyCreate, 
    req: Request,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """Create new API key with app association"""
    # 验证app_id是否存在且属于当前用户
    app = get_app(db, current_user_id, request.app_id)
    if not app:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="App not found or not owned by current user"  # 更明确的错误提示
        )

    new_api_key = generate_api_key()
    client_ip = req.client.host

    api_key_entry = APIKey(
        id=str(uuid.uuid4()),
        user_id=current_user_id,
        app_id=request.app_id,
        api_name=request.api_name,
        api_key=new_api_key,
        expiration=request.expiration,
        memo=request.memo,
        ip_addr=client_ip,
    )
    db.add(api_key_entry)
    db.commit()
    db.refresh(api_key_entry)

    return api_key_entry

@router.get("/list", response_model=List[APIKeyResponse])
async def get_api_keys(
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user)
):
    """Get user's API keys"""
    return db.query(APIKey).filter(APIKey.user_id == user_id).all()

@router.put("/update/{api_key_id}", response_model=APIKeyResponse)
async def update_api_key(
    api_key_id: str,
    update_data: APIKeyUpdate,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user)
):
    """Update API key metadata"""
    api_key_entry = db.query(APIKey).filter(
        APIKey.id == api_key_id,
        APIKey.user_id == user_id
    ).first()
    
    if not api_key_entry:
        raise HTTPException(status_code=404, detail="API key not found")
    
    for key, value in update_data.dict().items():
        setattr(api_key_entry, key, value)
    
    db.commit()
    db.refresh(api_key_entry)
    return api_key_entry

@router.delete("/delete/{api_key_id}")
async def delete_api_key(
    api_key_id: str, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user)
):
    """Delete API key"""
    api_key_entry = db.query(APIKey).filter(
        APIKey.id == api_key_id,
        APIKey.user_id == user_id
    ).first()
    
    if not api_key_entry:
        raise HTTPException(status_code=404, detail="API key not found")

    db.delete(api_key_entry)
    db.commit()
    return {"message": "API key deleted successfully"}