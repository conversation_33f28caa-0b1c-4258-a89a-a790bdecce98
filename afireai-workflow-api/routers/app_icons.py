from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from fastapi.responses import Response
from service.app_icon_service import upsert_icon, get_icon
from service.apps_service import update_app
from schemas.app_icon import AppIconOut
from database.db import get_db
from utils.auth import get_current_user

router = APIRouter(prefix="/apps")

@router.get("/{app_id}/icon", response_model=AppIconOut)
def fetch_icon(app_id: str, db: Session = Depends(get_db)):
    icon = get_icon(db, app_id)
    if not icon:
        raise HTTPException(status_code=404, detail="Icon not found")
    return icon

@router.get("/{app_id}/icon/raw")
def fetch_icon_raw(app_id: str, db: Session = Depends(get_db)):
    icon = get_icon(db, app_id)
    if not icon:
        raise HTTPException(status_code=404, detail="Icon not found")
    return Response(content=icon.icon_data, media_type=icon.mime_type)

@router.put("/{app_id}/icon", response_model=AppIconOut, status_code=200)
def update_icon(
    app_id: str,
    file: UploadFile = File(...),
    name: str | None = None,
    description: str | None = None,
    db: Session = Depends(get_db),
    user = Depends(get_current_user)
):
    icon = upsert_icon(db, app_id=app_id, user_id=user.id,
                       data=file.file.read(), mime=file.content_type)
    if name or description:
        update_app(db, app_id=app_id, name=name, description=description)
    return icon