from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List
from database.db import get_db
from utils.auth import get_current_user
from schemas.knowledge import KBCreate, KBUpdate, KBOut
from schemas.knowledge_process import KnowledgeProcessRequest, KnowledgeProcessResponse
from service.knowledge_service import create_kb, list_kb, update_kb, delete_kb
from service.knowledge_process_service import process_knowledge

router = APIRouter()

# ---- KB CRUD ----
@router.post("/create", response_model=KBOut)
def kb_create(payload: KBCreate, db: Session = Depends(get_db),
              req: Request = None, current_user_id: str = Depends(get_current_user)):
    return create_kb(db, current_user_id, payload, req.client.host)

@router.get("", response_model=List[KBOut])
def kb_list(db: Session=Depends(get_db), current_user_id: str = Depends(get_current_user)):
    return list_kb(db, current_user_id)

@router.patch("/{kb_id}", response_model=KBOut)
def kb_patch(kb_id: str, payload: KBUpdate, db: Session=Depends(get_db), current_user_id: str = Depends(get_current_user)):
    return update_kb(db, kb_id, payload, current_user_id)

@router.delete("/{kb_id}", status_code=status.HTTP_204_NO_CONTENT)
def kb_delete(kb_id: str, db: Session=Depends(get_db), current_user_id: str = Depends(get_current_user)):
    delete_kb(db, kb_id, current_user_id)

@router.post("/add/{kb_id}", response_model=KnowledgeProcessResponse)
def kb_process(kb_id: str, payload: KnowledgeProcessRequest, db: Session = Depends(get_db), current_user_id: str = Depends(get_current_user)):
    """ナレッジベースの処理（embedding生成とVector DB登録）"""
    return process_knowledge(db, kb_id, current_user_id, payload)

