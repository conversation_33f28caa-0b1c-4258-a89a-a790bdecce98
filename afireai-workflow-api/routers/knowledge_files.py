from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List
from database.db import get_db
from utils.auth import get_current_user
from schemas.knowledge_file import FileOut, ValidFlagUpdate
from service.knowledge_file_service import add_files, delete_file_by_id, list_files, update_valid_flag

router = APIRouter()

# ---- File Upload ----
@router.post("/{kb_id}/files", response_model=List[FileOut])
async def upload_files(
    kb_id: str,
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    req: Request = None,
    current_user_id: str = Depends(get_current_user)
):
    if len(files) == 0:
        raise HTTPException(400, "No files uploaded")
    return add_files(db, kb_id, current_user_id, files, req.client.host)

# ---- List Files ----
@router.get("/{kb_id}/files", response_model=List[FileOut])
def list_kb_files(kb_id: str, db: Session=Depends(get_db)):
    return list_files(db, kb_id) 

# ---- File Delete ----
@router.delete("/{file_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_file(file_id: str, db: Session = Depends(get_db), current_user_id: str = Depends(get_current_user)):
    delete_file_by_id(db, current_user_id, file_id)

# ---- Update Valid Flag ----
@router.patch("/{file_id}/valid-flag", response_model=FileOut)
def update_file_valid_flag(
    file_id: str,
    payload: ValidFlagUpdate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    file = update_valid_flag(db, current_user_id, file_id, payload.valid_flg)
    if not file:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="File not found")
    return file