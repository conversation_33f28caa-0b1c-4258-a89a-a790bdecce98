# routers/search_similar_images.py:
from fastapi import APIRouter, UploadFile, File, HTTPException, Query, status
from fastapi.responses import HTMLResponse
from typing import List, Dict
import os
import shutil # For removing temporary files on shutdown
import logging

# Import necessary components from your core image retrieval logic
from service.image_retrieval_logic import (
    ImageRetrievalSystem, 
    save_uploaded_file,
    build_feature_database,
    # setup_dummy_images, # Function to create dummy images for initial setup/testing
    IMAGE_DATABASE_DIR, # Configuration for the image database path
    TEMP_QUERY_UPLOAD_DIR # Configuration for temporary upload directory
)

logger = logging.getLogger(__name__) # Logger specific to this router

router = APIRouter()

# Global instance of the ImageRetrievalSystem.
# It's initialized to None and will be set up during the router's startup event.
image_retrieval_system: ImageRetrievalSystem | None = None

@router.on_event("startup")
async def startup_event_image_retrieval():
    """
    Initializes the ImageRetrievalSystem when this FastAPI router starts up.
    This ensures that the deep learning model and Faiss index are loaded or built
    only once when the application starts, rather than on every request.
    """
    logger.info("Image retrieval router startup event: Initializing Image Retrieval System...")
    try:
        # Optional: Call this function to create dummy images for testing if your
        # image_database directory is empty. Comment this out in production if
        # your database is always pre-populated.
        # setup_dummy_images() 

        global image_retrieval_system
        # Initialize the ImageRetrievalSystem, passing the base directory where
        # your image database is located.
        image_retrieval_system = ImageRetrievalSystem(database_dir=IMAGE_DATABASE_DIR)
        logger.info("Image Retrieval System initialized successfully.")
    except Exception as e:
        logger.error(f"Error initializing Image Retrieval System: {e}", exc_info=True)
        # It's crucial to handle startup failures. Depending on criticality, you might
        # want to re-raise the exception to prevent the FastAPI app from starting,
        # or implement a retry mechanism.

@router.on_event("shutdown")
async def shutdown_event_image_retrieval():
    """
    Cleans up any temporary files created during image uploads when the FastAPI
    application (and thus this router) shuts down.
    """
    logger.info("Image retrieval router shutdown event: Cleaning up temporary files...")
    if os.path.exists(TEMP_QUERY_UPLOAD_DIR):
        try:
            shutil.rmtree(TEMP_QUERY_UPLOAD_DIR)
            logger.info(f"Removed temporary directory: {TEMP_QUERY_UPLOAD_DIR}")
        except Exception as e:
            logger.error(f"Error removing temporary directory {TEMP_QUERY_UPLOAD_DIR}: {e}", exc_info=True)

@router.post(
    "/query",
    response_model=List[Dict[str, str]], 
    summary="Search Similar Images",
    status_code=status.HTTP_200_OK
)
async def search_similar_images_endpoint(
    file: UploadFile = File(..., description="The image file to search for similar images. Supported formats: JPG, PNG, GIF, BMP."),
    top_k: int = Query(5, ge=1, le=20, description="The number of top similar images to return. Max 20 for performance.")
) -> List[Dict[str, str]]:
    """
    Uploads an image file and searches for visually similar images within the pre-built image database.

    - **file**: The image file to be uploaded.
    - **top_k**: Specifies how many of the most similar images should be returned.

    **Returns:** A list of dictionaries, where each dictionary contains:
    - `image_path`: The file path to the similar image in the database.
    - `similarity_score`: A float string representing how similar the image is to the query image (higher is more similar, typically between 0.0 and 1.0).
    """

    build_feature_database(IMAGE_DATABASE_DIR)
    
    # Check if the image retrieval system is initialized.
    # This should always be true if startup_event runs successfully.
    if image_retrieval_system is None:
        logger.error("Attempted to query before Image Retrieval System was initialized.")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE, 
            detail="Image similarity service is not ready. Please try again in a moment."
        )

    # Validate file type
    if not file.content_type or not file.content_type.startswith('image/'):
        logger.warning(f"Invalid file type uploaded: {file.content_type}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail="Invalid file type. Only image files (e.g., image/jpeg, image/png) are allowed."
        )

    # Read the file content and save it temporarily
    file_content = await file.read()
    temp_file_path = save_uploaded_file(file_content, file.filename)
    logger.info(f"Uploaded file '{file.filename}' saved temporarily to '{temp_file_path}'")

    try:
        # Perform the similarity query using the initialized system
        results = image_retrieval_system.query(temp_file_path, top_k=top_k)
        
        # Format results for API response
        formatted_results = []
        for img_path, similarity in results:
            formatted_results.append({
                "image_path": img_path,
                "similarity_score": f"{similarity:.4f}" # Format score for display
            })
        
        logger.info(f"Query for '{file.filename}' completed. Found {len(formatted_results)} similar images.")
        return formatted_results
    except Exception as e:
        logger.error(f"Error during image similarity search for '{file.filename}': {e}", exc_info=True)
        # Catch any unexpected errors during the search process
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
            detail=f"An internal server error occurred during image similarity search. Please check logs for details."
        )
    finally:
        # Ensure the temporary file is deleted even if an error occurs
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
            logger.info(f"Cleaned up temporary file: {temp_file_path}")