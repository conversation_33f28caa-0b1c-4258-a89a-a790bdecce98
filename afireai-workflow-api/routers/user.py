# routers/user.py

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from database.db import get_db
from models.user import User
from schemas.user import UserCreate, UserResponse
from pydantic import BaseModel
from datetime import datetime, timedelta
from passlib.context import Crypt<PERSON>ontext


from utils.auth import create_tokens, verify_token, get_current_user, get_current_company_id

from models.tokens import Tokens


router = APIRouter()

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class LoginRequest(BaseModel):
    email: str
    password: str

class PasswordChangeRequest(BaseModel):
    old_password: str
    new_password: str

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"


# 全ユーザー情報を取得
@router.get("/", response_model=list[UserResponse])
async def get_users(db: Session = Depends(get_db)):
    return db.query(User).all()

# 特定のユーザー情報をIDで取得
@router.get("/{user_id}", response_model=UserResponse)
async def get_user(email: str, db: Session = Depends(get_db)):
    user = db.query(User).filter(User.email == email).first()
    if user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.post("/", response_model=UserResponse)
async def create_user(user: UserCreate, db: Session = Depends(get_db)):
    db_user = User(**user.dict())
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

@router.post("/login", response_model=TokenResponse)
async def login(request: LoginRequest, db: Session = Depends(get_db)):
    user = db.query(User).filter(User.email == request.email).first()

    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found.")

    # パスワードを検証
    if not pwd_context.verify(request.password, user.password):
        raise HTTPException(status_code=400, detail="password is incorrect.")

    # トークンを生成
    access_token, refresh_token = create_tokens(user.id)

    # トークンをDBに保存
    db_token = db.query(Tokens).filter(Tokens.user_id == user.id).first()
    if db_token:
        db_token.access_token = access_token
        db_token.refresh_token = refresh_token
        db_token.access_token_expiration = datetime.utcnow() + timedelta(minutes=120)
        db_token.refresh_token_expiration = datetime.utcnow() + timedelta(days=30)
    else:
        db_token = Tokens(
            user_id=user.id,
            access_token=access_token,
            refresh_token=refresh_token,
            access_token_expiration=datetime.utcnow() + timedelta(minutes=120),
            refresh_token_expiration=datetime.utcnow() + timedelta(days=30)
        )
        db.add(db_token)
    db.commit()

    return TokenResponse(access_token=access_token, refresh_token=refresh_token)

@router.post("/change-password")
async def change_password(
    request: PasswordChangeRequest,  # リクエストボディを取得
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)  # 認証されたユーザーのID
):
    # ユーザーを取得
    user = db.query(User).filter(User.id == current_user_id).first()

    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found.")

    # 旧パスワードを検証
    if not pwd_context.verify(request.old_password, user.password):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Old password is incorrect.")

    # 新しいパスワードをハッシュ化
    hashed_password = pwd_context.hash(request.new_password)

    # print(hashed_password)

    # 新しいパスワードを保存
    user.password = hashed_password
    user.updated_at = datetime.utcnow()
    db.commit()

    return {"message": "Password updated successfully."}