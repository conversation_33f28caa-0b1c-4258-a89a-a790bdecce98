# routers/workflow_run_nodes.py

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import uuid

from database.db import get_db
from models.workflow_run_nodes import WorkflowRunNode
from schemas.workflow_run_nodes import WorkflowRunNodeCreate, WorkflowRunNodeUpdate, WorkflowRunNodeResponse
from service.workflow_run_nodes_service import (
    create_workflow_run_node, 
    update_workflow_run_node, 
    get_workflow_run_node, 
    get_workflow_run_nodes_by_user,
    get_workflow_run_nodes_by_workflow_run,
    get_workflow_run_nodes_by_node,
    delete_workflow_run_node
)
from utils.auth import get_current_user

router = APIRouter()

@router.post("/create", response_model=WorkflowRunNodeResponse)
async def create_new_workflow_run_node(
    workflow_run_node_data: WorkflowRunNodeCreate,
    req: Request,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """新しいワークフロー実行ノードを作成する"""
    # ユーザーIDを現在認証されているユーザーに設定
    workflow_run_node_data.user_id = current_user_id
    # クライアントのIPアドレスを設定
    workflow_run_node_data.ip_addr = req.client.host
    
    return create_workflow_run_node(db, workflow_run_node_data)

@router.get("/list", response_model=List[WorkflowRunNodeResponse])
async def list_workflow_run_nodes(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """現在のユーザーのワークフロー実行ノード一覧を取得する"""
    return get_workflow_run_nodes_by_user(db, current_user_id, skip, limit)

@router.get("/workflow-run/{workflow_run_id}", response_model=List[WorkflowRunNodeResponse])
async def list_workflow_run_nodes_by_workflow_run(
    workflow_run_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたワークフロー実行IDに関連するワークフロー実行ノード一覧を取得する"""
    # ワークフロー実行の所有者チェックは省略（必要に応じて追加）
    nodes = get_workflow_run_nodes_by_workflow_run(db, workflow_run_id, skip, limit)
    
    # 権限チェック: 自分のワークフロー実行ノードのみアクセス可能
    # 最初のノードのユーザーIDをチェック（ノードが存在する場合）
    if nodes and nodes[0].user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access these workflow run nodes")
    
    return nodes

@router.get("/node/{node_id}", response_model=List[WorkflowRunNodeResponse])
async def list_workflow_run_nodes_by_node(
    node_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたノードIDに関連するワークフロー実行ノード一覧を取得する"""
    nodes = get_workflow_run_nodes_by_node(db, node_id, skip, limit)
    
    # 権限チェック: 自分のワークフロー実行ノードのみアクセス可能
    # 最初のノードのユーザーIDをチェック（ノードが存在する場合）
    if nodes and nodes[0].user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access these workflow run nodes")
    
    return nodes

@router.get("/{node_id}", response_model=WorkflowRunNodeResponse)
async def get_workflow_run_node_by_id(
    node_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDのワークフロー実行ノードを取得する"""
    db_workflow_run_node = get_workflow_run_node(db, node_id)
    
    if not db_workflow_run_node:
        raise HTTPException(status_code=404, detail="Workflow run node not found")
    
    # 権限チェック: 自分のワークフロー実行ノードのみアクセス可能
    if db_workflow_run_node.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this workflow run node")
    
    return db_workflow_run_node

@router.put("/{node_id}", response_model=WorkflowRunNodeResponse)
async def update_workflow_run_node_by_id(
    node_id: str,
    workflow_run_node_data: WorkflowRunNodeUpdate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDのワークフロー実行ノードを更新する"""
    # まず、ワークフロー実行ノードが存在するか確認
    db_workflow_run_node = get_workflow_run_node(db, node_id)
    
    if not db_workflow_run_node:
        raise HTTPException(status_code=404, detail="Workflow run node not found")
    
    # 権限チェック: 自分のワークフロー実行ノードのみ更新可能
    if db_workflow_run_node.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to update this workflow run node")
    
    # ワークフロー実行ノードを更新
    updated_workflow_run_node = update_workflow_run_node(db, node_id, workflow_run_node_data)
    
    return updated_workflow_run_node

@router.delete("/{node_id}")
async def delete_workflow_run_node_by_id(
    node_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDのワークフロー実行ノードを削除する"""
    # まず、ワークフロー実行ノードが存在するか確認
    db_workflow_run_node = get_workflow_run_node(db, node_id)
    
    if not db_workflow_run_node:
        raise HTTPException(status_code=404, detail="Workflow run node not found")
    
    # 権限チェック: 自分のワークフロー実行ノードのみ削除可能
    if db_workflow_run_node.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this workflow run node")
    
    # ワークフロー実行ノードを削除
    success = delete_workflow_run_node(db, node_id)
    
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete workflow run node")
    
    return {"message": "Workflow run node deleted successfully"}
