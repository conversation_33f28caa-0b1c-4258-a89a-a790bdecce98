# routers/workflow_runs.py

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import uuid
from datetime import datetime, timedelta

from database.db import get_db
from models.workflow_runs import WorkflowRun
from schemas.workflow_runs import WorkflowRunCreate, WorkflowRunUpdate, WorkflowRunResponse
from service.workflow_runs_service import (
    create_workflow_run,
    update_workflow_run,
    get_workflow_run,
    get_workflow_runs_by_user,
    get_workflow_runs_by_app,
    delete_workflow_run,
    get_workflow_runs_with_filters
)
from utils.auth import get_current_user

router = APIRouter()

@router.post("/create", response_model=WorkflowRunResponse)
async def create_new_workflow_run(
    workflow_run_data: WorkflowRunCreate,
    req: Request,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """新しいワークフロー実行を作成する"""
    # ユーザーIDを現在認証されているユーザーに設定
    workflow_run_data.user_id = current_user_id
    # クライアントのIPアドレスを設定
    workflow_run_data.ip_addr = req.client.host

    return create_workflow_run(db, workflow_run_data)

@router.get("/list", response_model=List[WorkflowRunResponse])
async def list_workflow_runs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """現在のユーザーのワークフロー実行一覧を取得する"""
    return get_workflow_runs_by_user(db, current_user_id, skip, limit)

@router.get("/app/{app_id}", response_model=List[WorkflowRunResponse])
async def list_workflow_runs_by_app(
    app_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたアプリIDに関連するワークフロー実行一覧を取得する"""
    # アプリの所有者チェックは省略（必要に応じて追加）
    return get_workflow_runs_by_app(db, app_id, skip, limit)

@router.get("/{workflow_run_id}", response_model=WorkflowRunResponse)
async def get_workflow_run_by_id(
    workflow_run_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDのワークフロー実行を取得する"""
    db_workflow_run = get_workflow_run(db, workflow_run_id)

    if not db_workflow_run:
        raise HTTPException(status_code=404, detail="Workflow run not found")

    # 権限チェック: 自分のワークフロー実行のみアクセス可能
    if db_workflow_run.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this workflow run")

    return db_workflow_run

@router.put("/{workflow_run_id}", response_model=WorkflowRunResponse)
async def update_workflow_run_by_id(
    workflow_run_id: str,
    workflow_run_data: WorkflowRunUpdate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDのワークフロー実行を更新する"""
    # まず、ワークフロー実行が存在するか確認
    db_workflow_run = get_workflow_run(db, workflow_run_id)

    if not db_workflow_run:
        raise HTTPException(status_code=404, detail="Workflow run not found")

    # 権限チェック: 自分のワークフロー実行のみ更新可能
    if db_workflow_run.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to update this workflow run")

    # ワークフロー実行を更新
    updated_workflow_run = update_workflow_run(db, workflow_run_id, workflow_run_data)

    return updated_workflow_run

@router.delete("/{workflow_run_id}")
async def delete_workflow_run_by_id(
    workflow_run_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDのワークフロー実行を削除する"""
    # まず、ワークフロー実行が存在するか確認
    db_workflow_run = get_workflow_run(db, workflow_run_id)

    if not db_workflow_run:
        raise HTTPException(status_code=404, detail="Workflow run not found")

    # 権限チェック: 自分のワークフロー実行のみ削除可能
    if db_workflow_run.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this workflow run")

    # ワークフロー実行を削除
    success = delete_workflow_run(db, workflow_run_id)

    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete workflow run")

    return {"message": "Workflow run deleted successfully"}

@router.get("/app/{app_id}/logs", response_model=dict)
async def get_app_workflow_logs(
    app_id: str,
    status: Optional[str] = Query(None, description="Filter by status: SUCCESS, FAILED, RUNNING, or ALL"),
    time_range: Optional[str] = Query(None, description="Time range: today, 7days, 1month, 6months, all"),
    search: Optional[str] = Query(None, description="Search keyword"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(30, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたアプリIDのワークフローログを取得する（フィルタリング、検索、ページネーション対応）"""

    # 時間範囲の計算
    start_date = None
    if time_range == "today":
        start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    elif time_range == "7days":
        start_date = datetime.now() - timedelta(days=7)
    elif time_range == "1month":
        start_date = datetime.now() - timedelta(days=30)
    elif time_range == "6months":
        start_date = datetime.now() - timedelta(days=180)
    # "all" の場合は start_date = None のまま

    # ステータスの正規化
    if status == "ALL":
        status = None

    # オフセットの計算
    skip = (page - 1) * page_size

    # フィルタリングされたデータを取得（app_idでフィルタ）
    logs, total_count = get_workflow_runs_with_filters(
        db=db,
        user_id=current_user_id,
        app_id=app_id,  # app_idを追加
        status=status,
        start_date=start_date,
        search=search,
        skip=skip,
        limit=page_size
    )

    # レスポンスデータの構築（SQLAlchemyオブジェクトをPydanticモデルに変換）
    logs_response = [WorkflowRunResponse.model_validate(log) for log in logs]

    return {
        "logs": logs_response,
        "pagination": {
            "page": page,
            "page_size": page_size,
            "total_count": total_count,
            "total_pages": (total_count + page_size - 1) // page_size
        }
    }

@router.get("/logs", response_model=dict)
async def get_workflow_logs(
    status: Optional[str] = Query(None, description="Filter by status: SUCCESS, FAILED, RUNNING, or ALL"),
    time_range: Optional[str] = Query(None, description="Time range: today, 7days, 1month, 6months, all"),
    search: Optional[str] = Query(None, description="Search keyword"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(30, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """ワークフローログを取得する（フィルタリング、検索、ページネーション対応）"""

    # 時間範囲の計算
    start_date = None
    if time_range == "today":
        start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    elif time_range == "7days":
        start_date = datetime.now() - timedelta(days=7)
    elif time_range == "1month":
        start_date = datetime.now() - timedelta(days=30)
    elif time_range == "6months":
        start_date = datetime.now() - timedelta(days=180)
    # "all" の場合は start_date = None のまま

    # ステータスの正規化
    if status == "ALL":
        status = None

    # オフセットの計算
    skip = (page - 1) * page_size

    # フィルタリングされたデータを取得
    logs, total_count = get_workflow_runs_with_filters(
        db=db,
        user_id=current_user_id,
        status=status,
        start_date=start_date,
        search=search,
        skip=skip,
        limit=page_size
    )

    # レスポンスデータの構築（SQLAlchemyオブジェクトをPydanticモデルに変換）
    logs_response = [WorkflowRunResponse.model_validate(log) for log in logs]

    return {
        "logs": logs_response,
        "pagination": {
            "page": page,
            "page_size": page_size,
            "total_count": total_count,
            "total_pages": (total_count + page_size - 1) // page_size
        }
    }
