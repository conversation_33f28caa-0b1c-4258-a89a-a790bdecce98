from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
import asyncio
import uuid # uuid is imported but not used in this snippet
from datetime import datetime, timezone
import json # json is imported but not used directly in this snippet
import logging
from typing import <PERSON>ple

from database.db import get_db
# These models are imported but not directly used in the run_workflow endpoint,
# likely used by the services or orchestrator
from models.workflow_runs import WorkflowRun  # 导入 WorkflowRun 模型
from models.workflow_run_nodes import WorkflowRunNode  # 导入 WorkflowRunNode 模型
from schemas.workflow_runs import WorkflowRunCreate
from service.apps_service import get_app
from service.workflow_runs_service import create_workflow_run
from utils.orchestrator import Orchestrator
from utils.auth import verify_api_key # This should now point to the corrected version

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/run/{workflow_id}", status_code=200)
async def run_workflow(
    workflow_id: str,
    req: Request,
    db: Session = Depends(get_db),
    api_key_info: Tuple[str, str] = Depends(verify_api_key),  # 修改依赖注入方式
):
    """ワークフローを実行する"""
    # 保持原有解包逻辑不变
    user_id, app_id = api_key_info

    # 修正app查询参数，传入正确的user_idとapp_id
    # Here, app_id_from_key (obtained from the API key) is used to get the app.
    # The docstring for workflow_id says "ワークフローID（app_id）".
    # If workflow_id from the path *must* match app_id_from_key, you might add a check:
    # if workflow_id != app_id_from_key:
    #     raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Workflow ID in path does not match API key's associated application.")
    
    app = get_app(db, user_id=user_id, app_id=app_id)

    if not app:
        # It might be more accurate to say "Application not found or not authorized for this API key"
        raise HTTPException(status_code=404, detail="Workflow (Application) not found")

    # flow_jsonの取得
    flow_json = app.flow_json

    # ワークフロー実行レコードの作成
    workflow_run_data = WorkflowRunCreate(
        type="API",  # 添加API类型标识
        user_id=user_id,
        app_id=app_id,
        status="RUNNING",
        params="",
        memory="",
        flow_json=flow_json,
        finished_at=datetime.now(timezone.utc),  # 仮の値、後で更新
        ip_addr=req.client.host
    )
    workflow_run = create_workflow_run(db, workflow_run_data)

    # オーケストレーターの初期化と実行
    orchestrator = Orchestrator(db)

    # 非同期タスクとしてワークフロー実行を開始
    # Note: workflow_id from the path is passed to orchestrator.run_workflow.
    # Ensure this is the intended ID for the orchestrator's context.
    asyncio.create_task(orchestrator.run_workflow(workflow_id, workflow_run.workflow_run_id, flow_json))

    # 即時レスポンスを返す
    return {
        "workflow_id": workflow_id, # Returns the workflow_id from the path
        "workflow_run_id": workflow_run.workflow_run_id,
        "status": workflow_run.status # Indicates the task was successfully created, not that the workflow completed.
    }


@router.get("/run/{workflow_id}/{workflow_run_id}", status_code=200)
async def get_workflow_run_details(
    workflow_id: str,
    workflow_run_id: str,
    db: Session = Depends(get_db),
    api_key_info: Tuple[str, str] = Depends(verify_api_key),  # 修改依赖注入方式
):
    """获取指定 workflow_run_id 的详细信息及其所有节点信息"""
    user_id, app_id = api_key_info

    # 查询 workflow_runs 表获取 workflow_run_id 对应的记录
    workflow_run = db.query(WorkflowRun).filter(
        WorkflowRun.user_id == user_id,
        WorkflowRun.app_id == workflow_id,
        WorkflowRun.workflow_run_id == workflow_run_id
    ).first()

    if not workflow_run:
        raise HTTPException(status_code=404, detail="Workflow run not found")

    # 查询 workflow_run_nodes 表获取该 workflow_run_id 的所有节点信息
    nodes = db.query(WorkflowRunNode).filter(
        WorkflowRunNode.user_id == user_id,
        WorkflowRunNode.workflow_run_id == workflow_run_id
    ).all()

    # 构造响应数据
    response_data = {
        "app_id": workflow_run.app_id,
        "workflow_run_id": workflow_run.workflow_run_id,
        "type": workflow_run.type,
        "status": workflow_run.status,
        "started_at": workflow_run.started_at.isoformat() if workflow_run.started_at else None,
        "finished_at": workflow_run.finished_at.isoformat() if workflow_run.finished_at else None,
        "nodes": [
            {
                "node_id": node.node_id,
                "node_ver": node.node_ver,
                "status": node.status,
                "started_at": node.started_at.isoformat() if node.started_at else None,
                "finished_at": node.finished_at.isoformat() if node.finished_at else None
            } for node in nodes
        ]
    }

    return response_data
