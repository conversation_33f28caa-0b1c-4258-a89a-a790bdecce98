from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class APIKeyBase(BaseModel):
    app_id: str
    api_name: Optional[str] = None
    expiration: Optional[datetime] = None
    memo: Optional[str] = None

class APIKeyCreate(APIKeyBase):
    pass

class APIKeyUpdate(APIKeyBase):
    pass

class APIKeyResponse(APIKeyBase):
    id: str
    user_id: str
    api_key: str
    ip_addr: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True