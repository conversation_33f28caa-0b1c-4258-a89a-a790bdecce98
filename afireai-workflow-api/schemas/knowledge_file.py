from pydantic import BaseModel
from datetime import datetime

class ValidFlagUpdate(BaseModel):
    valid_flg: str

class FileOut(BaseModel):
    file_id: str
    kb_id: str
    user_id: str
    file_name: str
    file_type: str
    file_size: int | None = None
    storage_path: str | None = None
    status: str | None = None
    chunk_identifier: str | None = None
    uploaded_by: str | None = None
    ip_addr: str | None = None
    valid_flg: str | None = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True