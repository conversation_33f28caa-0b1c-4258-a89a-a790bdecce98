from pydantic import BaseModel
from typing import Optional

class KnowledgeProcessRequest(BaseModel):
    embedding_model: str
    chunk_identifier: str
    chunk_size: int
    chunk_overlap: int
    remove_line_breaks: Optional[bool] = True
    remove_urls: Optional[bool] = False

class KnowledgeProcessResponse(BaseModel):
    success: bool
    message: str
    processed_files: int
    total_chunks: int