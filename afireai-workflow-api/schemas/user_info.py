from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class UserInfoBase(BaseModel):
    user_id: str
    company_id: str
    nick_name: Optional[str]
    last_nm_kanji: Optional[str]
    first_nm_kanji: Optional[str]
    last_nm_kana: Optional[str]
    first_nm_kana: Optional[str]
    email: Optional[str]
    phone: Optional[str]
    sex: Optional[str]
    birth: Optional[str]
    address: Optional[str]
    is_delete: Optional[str]
    ip_addr: Optional[str]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

    class Config:
        # orm_mode = True
        from_attributes = True  # Pydantic v2対応

class UserInfoCreate(UserInfoBase):
    pass

class UserInfoUpdate(BaseModel):
    nick_name: Optional[str]
    email: Optional[str]
    phone: Optional[str]
    address: Optional[str]