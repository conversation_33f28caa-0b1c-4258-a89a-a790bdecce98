# schemas/workflow_runs.py

from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class WorkflowRunBase(BaseModel):
    user_id: Optional[str] = None
    app_id: str
    workflow_run_id: str
    type: str
    status: Optional[str] = None
    params: Optional[str] = None
    memory: Optional[str] = None
    flow_json: str
    ip_addr: Optional[str] = None

class WorkflowRunCreate(BaseModel):
    user_id: str = None
    app_id: str
    type: str = Field(default="WEB", description="运行类型(API/WEB/TEST等)")
    status: str = None
    params: str = None
    memory: str = None
    flow_json: str
    finished_at: Optional[datetime] = Field(default=None, description="完成时间（系统自动更新）")  # 修改为可选字段
    ip_addr: str = None

class WorkflowRunUpdate(BaseModel):
    status: Optional[str] = None
    params: Optional[str] = None
    memory: Optional[str] = None
    flow_json: Optional[str] = None
    finished_at: Optional[datetime] = None

class WorkflowRunResponse(WorkflowRunBase):
    started_at: datetime
    finished_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True