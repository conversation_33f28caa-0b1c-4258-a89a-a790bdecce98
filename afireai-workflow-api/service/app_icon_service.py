from sqlalchemy.orm import Session
from models.app_icon import AppIcon

def upsert_icon(db: Session, *, app_id: str, user_id: str, data: bytes, mime: str):
    icon = db.query(AppIcon).filter(AppIcon.app_id == app_id).first()  # 修改查询条件
    if icon:
        icon.icon_data = data
        icon.mime_type = mime
    else:
        icon = AppIcon(app_id=app_id, user_id=user_id, icon_data=data, mime_type=mime)
        db.add(icon)
    db.commit()
    db.refresh(icon)
    return icon

def get_icon(db: Session, app_id: str) -> AppIcon | None:
    return db.query(AppIcon).get(app_id)