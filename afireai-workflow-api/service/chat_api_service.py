from openai import OpenAI
import os
from dotenv import load_dotenv  # dotenvが必要
import urllib3
import aiohttp
from datetime import datetime
import asyncio

load_dotenv()  # .envファイルを読み込み

client = OpenAI(api_key=os.getenv("__OPENAI_API_KEY"))
MODEL_CHAT = os.environ.get("__LLM_MODEL_CHAT")

# local_deepseek_model の設定
local_deepseek_model = os.environ.get("__LLM_MODEL_LOCAL_DEEPSEEK")
local_deepseek_api_url = os.environ.get("__API_URL_LOCAL_DEEPSEEK")

# SSL警告を無効化（verify=False使用時）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

async def call_openai_api(prompt: str) -> dict:
    """
    OpenAI API を呼び出し、結果を返す関数。
    成功時は {"success": True, "response": "生成された回答"}
    エラー時は {"success": False, "error": "エラーメッセージ"} を返す。

    Args:
        prompt (str): ユーザーの入力プロンプト

    Returns:
        dict: 成功時 {"success": True, "response": str}, 失敗時 {"success": False, "error": str}
    """
    try:
        completion = client.chat.completions.create(
            model=MODEL_CHAT,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ],
        )
        answer = completion.choices[0].message.content
        return {"success": True, "response": answer}
    except Exception as e:
        return {"success": False, "error": f"Unexpected error: {str(e)}"}
