# service/image_retrieval_logic.py

import torch
import torchvision.models as models
from torchvision import transforms
from PIL import Image, ImageDraw # Added ImageDraw for dummy images
import os
import numpy as np
import faiss
from tqdm import tqdm
import json
import uuid # For generating unique filenames for uploaded images

# --- 1. Configuration and Global Setup ---
# These settings can be adjusted based on your environment and needs.

# Device for model inference (GPU if available, otherwise CPU)
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {DEVICE}")

# Path to your image library directory
IMAGE_DATABASE_DIR = "image_database"

# Path to directory for temporarily storing uploaded query images
TEMP_QUERY_UPLOAD_DIR = "temp_query_uploads"

# Paths for saving/loading the Faiss index and image ID mapping
FAISS_INDEX_PATH = "image_features.faiss_index"
IMAGE_IDS_MAP_PATH = "image_ids_map.json"

# Feature dimension of the chosen model (ResNet50 has 2048)
FEATURE_DIM = 2048

# --- 2. Feature Extraction Module ---

# Load pre-trained ResNet50 model
MODEL = models.resnet50(weights=models.ResNet50_Weights.IMAGENET1K_V1)
MODEL = torch.nn.Sequential(*(list(MODEL.children())[:-1]))
MODEL.eval()
MODEL.to(DEVICE)

# Image preprocessing pipeline
PREPROCESS = transforms.Compose([
    transforms.Resize(256),
    transforms.CenterCrop(224),
    transforms.ToTensor(),
    transforms.Normalize(
        mean=[0.485, 0.456, 0.406],
        std=[0.229, 0.224, 0.225]
    )
])

def extract_features(image_path: str) -> np.ndarray | None:
    """
    Extracts L2-normalized feature vector from a given image path.
    """
    try:
        image = Image.open(image_path).convert('RGB')
    except Exception as e:
        print(f"Error opening image {image_path}: {e}")
        return None

    image_tensor = PREPROCESS(image).unsqueeze(0).to(DEVICE)
    
    with torch.no_grad():
        features = MODEL(image_tensor)
    
    features = features.squeeze().cpu().numpy()
    
    norm = np.linalg.norm(features)
    if norm == 0:
        print(f"Warning: Feature vector for {image_path} has zero norm. Skipping.")
        return None
    
    return (features / norm).astype('float32')

# --- 3. Feature Database Building Module ---

def build_feature_database(image_dir: str) -> tuple[faiss.Index | None, list[str] | None]:
    """
    Traverses the image directory, extracts features for all images,
    builds a Faiss IndexFlatIP, and saves it along with image ID mappings.
    """
    image_paths_full = [os.path.join(image_dir, f) 
                        for f in os.listdir(image_dir) 
                        if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]

    if not image_paths_full:
        print(f"Warning: No images found in {image_dir}. Database will be empty.")
        return None, None

    all_features = []
    all_image_ids = [] 

    print(f"Starting feature extraction for {len(image_paths_full)} images in {image_dir}...")
    for img_path_full in tqdm(image_paths_full, desc="Extracting features"):
        features = extract_features(img_path_full)
        if features is not None:
            all_features.append(features)
            all_image_ids.append(os.path.basename(img_path_full))
        # else:
        #     print(f"Skipping {img_path_full} due to feature extraction failure.")

    if not all_features:
        print("No features extracted successfully. Database building failed.")
        return None, None

    all_features_array = np.array(all_features).astype('float32')
    print(f"Successfully extracted features for {len(all_features_array)} images.")

    index = faiss.IndexFlatIP(FEATURE_DIM) 
    index.add(all_features_array)
    
    faiss.write_index(index, FAISS_INDEX_PATH)
    
    with open(IMAGE_IDS_MAP_PATH, 'w') as f:
        json.dump(all_image_ids, f)
    
    print(f"Faiss index saved to {FAISS_INDEX_PATH}")
    print(f"Image IDs map saved to {IMAGE_IDS_MAP_PATH}")
    
    return index, all_image_ids

# --- 4. Image Retrieval System Class ---

class ImageRetrievalSystem:
    def __init__(self, database_dir: str = IMAGE_DATABASE_DIR, 
                 faiss_index_path: str = FAISS_INDEX_PATH, 
                 image_ids_map_path: str = IMAGE_IDS_MAP_PATH):
        self.database_dir = database_dir
        self.faiss_index_path = faiss_index_path
        self.image_ids_map_path = image_ids_map_path
        
        self.index: faiss.Index | None = None
        self.image_ids: list[str] | None = None
        
        self._load_or_build_database()

    def _load_or_build_database(self):
        """
        Internal method to load or build the Faiss index and image ID map.
        """
        if os.path.exists(self.faiss_index_path) and os.path.exists(self.image_ids_map_path):
            print("Loading existing Faiss index and image IDs...")
            self.index = faiss.read_index(self.faiss_index_path)
            with open(self.image_ids_map_path, 'r') as f:
                self.image_ids = json.load(f)
            print(f"Loaded index with {self.index.ntotal} images.")
        else:
            print("Faiss index or image IDs map not found. Building new database...")
            self.index, self.image_ids = build_feature_database(self.database_dir)
            if self.index is None or self.image_ids is None:
                raise RuntimeError("Failed to build image feature database. Please check IMAGE_DATABASE_DIR.")
            print("Database built successfully.")

    def query(self, query_image_path: str, top_k: int = 5) -> list[tuple[str, float]]:
        """
        Queries the image database for images similar to the given query image.
        """
        if self.index is None or self.image_ids is None:
            print("Error: Image retrieval system not initialized properly. Index or IDs are missing.")
            return []

        print(f"Querying for similar images to: {query_image_path}")
        query_vec = extract_features(query_image_path)
        if query_vec is None:
            print(f"Could not extract features from {query_image_path}. Skipping query.")
            return []

        query_vec_reshaped = query_vec.reshape(1, -1) 
        
        distances, indices = self.index.search(query_vec_reshaped, top_k)
        
        results = []
        for i in range(len(indices[0])):
            idx = indices[0][i]
            similarity = float(distances[0][i])
            
            img_id = self.image_ids[idx]
            full_img_path = os.path.join(self.database_dir, img_id)
            results.append((full_img_path, similarity))
            
        return results

# --- Helper function for FastAPI to handle uploaded files ---
def save_uploaded_file(file_content: bytes, filename: str) -> str:
    """
    Saves an uploaded file temporarily and returns its path.
    """
    os.makedirs(TEMP_QUERY_UPLOAD_DIR, exist_ok=True)
    unique_filename = f"{uuid.uuid4()}_{filename}"
    file_path = os.path.join(TEMP_QUERY_UPLOAD_DIR, unique_filename)
    with open(file_path, "wb") as f:
        f.write(file_content)
    return file_path
