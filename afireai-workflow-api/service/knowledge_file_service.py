from sqlalchemy.orm import Session
from models.knowledge_file import KnowledgeFile
from utils.file_upload import save_to_kb
import os

def add_files(db: Session, kb_id: str, user_id: str, files, ip):
    out = []
    for f in files:
        path = save_to_kb(kb_id, f)
        meta = KnowledgeFile(
            kb_id=kb_id, user_id=user_id,
            file_name=f.filename, file_type=f.content_type,
            file_size=getattr(f, "spool_max_size", None),
            storage_path=path, ip_addr=ip
        )
        db.add(meta); out.append(meta)
    db.commit()
    return out

def list_files(db: Session, kb_id: str):
    return db.query(KnowledgeFile).filter_by(kb_id=kb_id).all()

def delete_file_by_id(db: Session, user_id: str, file_id: str):
    file = db.query(KnowledgeFile).filter_by(file_id=file_id, user_id=user_id).first()
    if file:
        # 删除物理文件
        if file.storage_path and os.path.exists(file.storage_path):
            os.remove(file.storage_path)
        db.delete(file)
        db.commit()