from sqlalchemy.orm import Session
from models.knowledge_file import KnowledgeFile
from utils.file_upload import save_to_kb
import os

def add_files(db: Session, kb_id: str, user_id: str, files, ip):
    out = []
    for f in files:
        path = save_to_kb(kb_id, f)
        meta = KnowledgeFile(
            kb_id=kb_id, user_id=user_id,
            file_name=f.filename, file_type=f.content_type,
            file_size=getattr(f, "spool_max_size", None),
            storage_path=path, ip_addr=ip,
            valid_flg="1"  # 有効区分を明示的に設定
        )
        db.add(meta); out.append(meta)
    db.commit()
    return out

def list_files(db: Session, kb_id: str):
    return db.query(KnowledgeFile).filter_by(kb_id=kb_id).all()

def list_valid_files(db: Session, kb_id: str):
    """有効なファイルのみを取得（処理用）"""
    return db.query(KnowledgeFile).filter_by(kb_id=kb_id, valid_flg="1").all()

def delete_file_by_id(db: Session, user_id: str, file_id: str):
    file = db.query(KnowledgeFile).filter_by(file_id=file_id, user_id=user_id, valid_flg="1").first()
    if file:
        # ソフト削除: valid_flgを"0"に設定
        file.valid_flg = "0"
        db.commit()
        return True
    return False

def update_valid_flag(db: Session, user_id: str, file_id: str, valid_flg: str):
    """ファイルの有効フラグを更新"""
    file = db.query(KnowledgeFile).filter_by(file_id=file_id, user_id=user_id).first()
    if file:
        file.valid_flg = valid_flg
        db.commit()
        db.refresh(file)
        return file
    return None

def hard_delete_file_by_id(db: Session, user_id: str, file_id: str):
    """物理削除用の関数（必要に応じて使用）"""
    file = db.query(KnowledgeFile).filter_by(file_id=file_id, user_id=user_id).first()
    if file:
        # 物理ファイルを削除
        if file.storage_path and os.path.exists(file.storage_path):
            os.remove(file.storage_path)
        db.delete(file)
        db.commit()
        return True
    return False