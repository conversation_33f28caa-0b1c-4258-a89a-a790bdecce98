import os
import re
import json
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
import openai
import PyPDF2
from models.knowledge import Knowledge
from models.knowledge_file import KnowledgeFile
from models.knowledge_chunks import KnowledgeChunk
from schemas.knowledge_process import KnowledgeProcessRequest
from utils.vector_db import vector_db
from utils.file import get_file_type

# OpenAI APIキーの設定
openai.api_key = os.getenv("__OPENAI_API_KEY")

def process_knowledge(db: Session, kb_id: str, user_id: str, request: KnowledgeProcessRequest):
    """ナレッジベースの処理メイン関数"""
    
    # 1. knowledge表の更新
    kb = db.query(Knowledge).filter_by(kb_id=kb_id, user_id=user_id).first()
    if not kb:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Knowledge base not found")
    
    # knowledge表を更新
    kb.embedding_model = request.embedding_model
    kb.chunk_identifier = request.chunk_identifier
    kb.chunk_size = request.chunk_size
    kb.chunk_overlap = request.chunk_overlap
    db.commit()
    
    # 2. knowledge_filesからファイルリストを取得
    files = db.query(KnowledgeFile).filter_by(kb_id=kb_id).all()
    if not files:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No files found for processing")
    
    processed_files = 0
    total_chunks = 0
    
    # 3. ファイルごとの処理ループ
    for file in files:
        try:
            # ファイル処理状態を更新
            file.status = "INGESTING"
            db.commit()
            
            # ファイル内容を読み取り
            file_content = read_file_content(file.storage_path)
            if not file_content:
                file.status = "ERROR"
                db.commit()
                continue
            
            # テキストの前処理
            processed_content = preprocess_text(
                file_content, 
                request.remove_line_breaks, 
                request.remove_urls
            )
            
            # チャンクに分割
            chunks = split_text_into_chunks(
                processed_content,
                request.chunk_size,
                request.chunk_overlap,
                request.chunk_identifier
            )
            
            # 各チャンクを処理
            file_chunks = []
            for i, chunk_text in enumerate(chunks):
                # embedding生成
                embedding = generate_embedding(chunk_text, request.embedding_model)
                if not embedding:
                    continue
                
                # knowledge_chunksに保存
                chunk = KnowledgeChunk(
                    kb_id=kb_id,
                    file_id=file.file_id,
                    user_id=user_id,
                    chunk_text=chunk_text,
                    chunk_index=i,
                    chunk_metadata=json.dumps({
                        "file_name": file.file_name,
                        "file_type": file.file_type,
                        "chunk_size": len(chunk_text)
                    })
                )
                db.add(chunk)
                db.commit()
                db.refresh(chunk)
                
                # Vector DBに登録するためのデータを準備
                file_chunks.append({
                    "vector": embedding,
                    "kb_id": kb_id,
                    "file_id": file.file_id,
                    "chunk_id": chunk.chunk_id,
                    "text": chunk_text,
                    "metadata": {
                        "file_name": file.file_name,
                        "file_type": file.file_type,
                        "chunk_index": i
                    }
                })
            
            # Vector DBに一括登録
            if file_chunks:
                success = vector_db.add_vectors(file_chunks)
                if success:
                    file.status = "DONE"
                    processed_files += 1
                    total_chunks += len(file_chunks)
                else:
                    file.status = "ERROR"
            else:
                file.status = "ERROR"
            
            db.commit()
            
        except Exception as e:
            print(f"Error processing file {file.file_id}: {e}")
            file.status = "ERROR"
            db.commit()
    
    return {
        "success": True,
        "message": f"Processing completed. {processed_files} files processed successfully.",
        "processed_files": processed_files,
        "total_chunks": total_chunks
    }

def read_file_content(storage_path: str) -> str:
    """ファイル内容を読み取り"""
    try:
        if not storage_path or not os.path.exists(storage_path):
            return ""
        
        # ファイル拡張子から種類を判定
        file_extension = os.path.splitext(storage_path)[1].lower()
        
        # PDFファイルの処理
        if file_extension == '.pdf':
            return read_pdf_content(storage_path)
        
        # テキストファイルの処理
        elif file_extension in ['.txt', '.md', '.csv', '.json', '.xml', '.html', '.htm']:
            with open(storage_path, 'r', encoding='utf-8') as f:
                return f.read()
        
        # その他のファイルは一旦UTF-8で試行し、失敗したら他のエンコーディングを試す
        else:
            encodings = ['utf-8', 'utf-16', 'shift_jis', 'gbk', 'latin-1']
            for encoding in encodings:
                try:
                    with open(storage_path, 'r', encoding=encoding) as f:
                        return f.read()
                except UnicodeDecodeError:
                    continue
            
            # すべてのエンコーディングで失敗した場合
            print(f"Could not decode file {storage_path} with any supported encoding")
            return ""
            
    except Exception as e:
        print(f"Error reading file {storage_path}: {e}")
        return ""

def read_pdf_content(pdf_path: str) -> str:
    """PDFファイルからテキストを抽出"""
    try:
        text = ""
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"
        
        return text.strip()
    except Exception as e:
        print(f"Error reading PDF file {pdf_path}: {e}")
        return ""

def preprocess_text(text: str, remove_line_breaks: bool = True, remove_urls: bool = False) -> str:
    """テキストの前処理"""
    if remove_line_breaks:
        # 連続するスペース、改行、タブを置換
        text = re.sub(r'\s+', ' ', text)
    
    if remove_urls:
        # URLとメールアドレスを削除
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', text)
    
    return text.strip()

def split_text_into_chunks(text: str, chunk_size: int, chunk_overlap: int, chunk_identifier: str) -> List[str]:
    """テキストをチャンクに分割"""
    chunks = []
    
    # チャンク識別子で分割
    if chunk_identifier and chunk_identifier in text:
        sections = text.split(chunk_identifier)
    else:
        sections = [text]
    
    current_chunk = ""
    
    for section in sections:
        section = section.strip()
        if not section:
            continue
        
        # セクションがチャンクサイズより小さい場合
        if len(current_chunk) + len(section) + len(chunk_identifier) <= chunk_size:
            if current_chunk:
                current_chunk += chunk_identifier + section
            else:
                current_chunk = section
        else:
            # 現在のチャンクを保存
            if current_chunk:
                chunks.append(current_chunk)
            
            # 新しいチャンクを開始
            if len(section) <= chunk_size:
                current_chunk = section
            else:
                # セクションが大きすぎる場合は分割
                words = section.split()
                temp_chunk = ""
                
                for word in words:
                    if len(temp_chunk) + len(word) + 1 <= chunk_size:
                        if temp_chunk:
                            temp_chunk += " " + word
                        else:
                            temp_chunk = word
                    else:
                        if temp_chunk:
                            chunks.append(temp_chunk)
                        temp_chunk = word
                
                current_chunk = temp_chunk
    
    # 最後のチャンクを追加
    if current_chunk:
        chunks.append(current_chunk)
    
    # オーバーラップ処理
    if chunk_overlap > 0 and len(chunks) > 1:
        overlapped_chunks = []
        for i, chunk in enumerate(chunks):
            if i == 0:
                overlapped_chunks.append(chunk)
            else:
                # 前のチャンクの末尾部分を取得
                prev_chunk = chunks[i-1]
                overlap_text = prev_chunk[-chunk_overlap:] if len(prev_chunk) > chunk_overlap else prev_chunk
                overlapped_chunk = overlap_text + " " + chunk
                overlapped_chunks.append(overlapped_chunk)
        
        return overlapped_chunks
    
    return chunks

def generate_embedding(text: str, model: str) -> List[float]:
    """OpenAI APIを使用してembeddingを生成"""
    try:
        # モデル名からOpenAI形式に変換
        if model.startswith("openai://"):
            openai_model = model.replace("openai://", "")
        else:
            openai_model = model
        
        response = openai.embeddings.create(
            model=openai_model,
            input=text
        )
        
        return response.data[0].embedding
    except Exception as e:
        print(f"Error generating embedding: {e}")
        return []