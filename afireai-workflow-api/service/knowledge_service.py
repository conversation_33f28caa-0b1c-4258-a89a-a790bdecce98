from sqlalchemy.orm import Session
from models.knowledge import Knowledge
from schemas.knowledge import KBCreate, KBUpdate

def create_kb(db: Session, user_id: str, data: KBCreate, ip: str):
    data_dict = data.dict(exclude={"ip_addr"})
    kb = Knowledge(user_id=user_id, ip_addr=ip, **data_dict)
    db.add(kb); db.commit(); db.refresh(kb)
    return kb

def list_kb(db: Session, user_id: str):
    return db.query(Knowledge).filter_by(user_id=user_id).all()

def get_kb(db: Session, kb_id: str, user_id: str):
    kb = db.query(Knowledge).filter_by(kb_id=kb_id, user_id=user_id).first()
    if not kb:
        from fastapi import HTTPException, status
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Knowledge base not found or you don't have permission")
    return kb

def update_kb(db: Session, kb_id: str, payload: KBUpdate, user_id: str):
    kb = db.query(Knowledge).filter_by(kb_id=kb_id, user_id=user_id).first()
    if not kb:
        from fastapi import HTTPException, status
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Knowledge base not found or you don't have permission")
    for k, v in payload.dict(exclude_unset=True).items():
        setattr(kb, k, v)
    db.commit(); db.refresh(kb)
    return kb

def delete_kb(db: Session, kb_id: str, user_id: str):
    kb = db.query(Knowledge).filter_by(kb_id=kb_id, user_id=user_id).first()
    if not kb:
        from fastapi import HTTPException, status
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Knowledge base not found or you don't have permission")
    db.delete(kb); db.commit()