# service/variables_service.py

import uuid
from sqlalchemy.orm import Session
from sqlalchemy import select, desc
from fastapi import HTTPException, status
from models.variables import Variable
from schemas.variables import VariableCreate, VariableUpdate
from datetime import datetime, timezone
from typing import List, Optional, Dict

# Function IDs
FUNC_CREATE_VARIABLE = "VAR001"
FUNC_UPDATE_VARIABLE = "VAR002"
FUNC_GET_VARIABLE = "VAR003"
FUNC_LIST_VARIABLES = "VAR004"
FUNC_DELETE_VARIABLE = "VAR005"
FUNC_LIST_VARIABLES_BY_WORKFLOW_RUN = "VAR006"
FUNC_GET_VARIABLE_BY_KEY = "VAR007"

def create_variable(db: Session, variable_data: VariableCreate) -> Variable:
    """
    新しい変数を作成する
    
    Args:
        db (Session): データベースセッション
        variable_data (VariableCreate): 作成する変数のデータ
        
    Returns:
        Variable: 作成された変数のモデル
    """
    variable_id = str(uuid.uuid4())
    
    db_variable = Variable(
        id=variable_id,
        user_id=variable_data.user_id,
        workflow_run_id=variable_data.workflow_run_id,
        var_key=variable_data.var_key,
        var_val=variable_data.var_val,
        ip_addr=variable_data.ip_addr
    )
    
    db.add(db_variable)
    db.commit()
    db.refresh(db_variable)
    
    return db_variable

def update_variable(db: Session, variable_id: str, variable_data: VariableUpdate) -> Optional[Variable]:
    """
    既存の変数を更新する
    
    Args:
        db (Session): データベースセッション
        variable_id (str): 更新する変数のID
        variable_data (VariableUpdate): 更新するデータ
        
    Returns:
        Optional[Variable]: 更新された変数のモデル、存在しない場合はNone
    """
    db_variable = db.query(Variable).filter(Variable.id == variable_id).first()
    
    if not db_variable:
        return None
    
    # 更新するフィールドを設定
    db_variable.var_val = variable_data.var_val
    db_variable.updated_at = datetime.now(timezone.utc)
    
    db.commit()
    db.refresh(db_variable)
    
    return db_variable

def update_variable_by_key(db: Session, workflow_run_id: str, var_key: str, variable_data: VariableUpdate) -> Optional[Variable]:
    """
    ワークフロー実行IDとキーで変数を更新する
    
    Args:
        db (Session): データベースセッション
        workflow_run_id (str): ワークフロー実行ID
        var_key (str): 変数キー
        variable_data (VariableUpdate): 更新するデータ
        
    Returns:
        Optional[Variable]: 更新された変数のモデル、存在しない場合はNone
    """
    db_variable = db.query(Variable).filter(
        Variable.workflow_run_id == workflow_run_id,
        Variable.var_key == var_key
    ).first()
    
    if not db_variable:
        return None
    
    # 更新するフィールドを設定
    db_variable.var_val = variable_data.var_val
    db_variable.updated_at = datetime.now(timezone.utc)
    
    db.commit()
    db.refresh(db_variable)
    
    return db_variable

def get_variable(db: Session, variable_id: str) -> Optional[Variable]:
    """
    指定されたIDの変数を取得する
    
    Args:
        db (Session): データベースセッション
        variable_id (str): 取得する変数のID
        
    Returns:
        Optional[Variable]: 取得した変数のモデル、存在しない場合はNone
    """
    return db.query(Variable).filter(Variable.id == variable_id).first()

def get_variable_by_key(db: Session, workflow_run_id: str, var_key: str) -> Optional[Variable]:
    """
    ワークフロー実行IDとキーで変数を取得する
    
    Args:
        db (Session): データベースセッション
        workflow_run_id (str): ワークフロー実行ID
        var_key (str): 変数キー
        
    Returns:
        Optional[Variable]: 取得した変数のモデル、存在しない場合はNone
    """
    return db.query(Variable).filter(
        Variable.workflow_run_id == workflow_run_id,
        Variable.var_key == var_key
    ).first()

def get_variables_by_user(db: Session, user_id: str, skip: int = 0, limit: int = 100) -> List[Variable]:
    """
    指定されたユーザーIDに関連するすべての変数を取得する
    
    Args:
        db (Session): データベースセッション
        user_id (str): ユーザーID
        skip (int): スキップする件数
        limit (int): 取得する最大件数
        
    Returns:
        List[Variable]: 変数のリスト
    """
    return db.query(Variable).filter(Variable.user_id == user_id).order_by(desc(Variable.created_at)).offset(skip).limit(limit).all()

def get_variables_by_workflow_run(db: Session, workflow_run_id: str, skip: int = 0, limit: int = 100) -> List[Variable]:
    """
    指定されたワークフロー実行IDに関連するすべての変数を取得する
    
    Args:
        db (Session): データベースセッション
        workflow_run_id (str): ワークフロー実行ID
        skip (int): スキップする件数
        limit (int): 取得する最大件数
        
    Returns:
        List[Variable]: 変数のリスト
    """
    return db.query(Variable).filter(Variable.workflow_run_id == workflow_run_id).order_by(desc(Variable.created_at)).offset(skip).limit(limit).all()

def get_variables_dict_by_workflow_run(db: Session, workflow_run_id: str) -> Dict[str, str]:
    """
    指定されたワークフロー実行IDに関連するすべての変数を辞書形式で取得する
    
    Args:
        db (Session): データベースセッション
        workflow_run_id (str): ワークフロー実行ID
        
    Returns:
        Dict[str, str]: キーと値のペアの辞書
    """
    variables = db.query(Variable).filter(Variable.workflow_run_id == workflow_run_id).all()
    return {var.var_key: var.var_val for var in variables}

def delete_variable(db: Session, variable_id: str) -> bool:
    """
    指定されたIDの変数を削除する
    
    Args:
        db (Session): データベースセッション
        variable_id (str): 削除する変数のID
        
    Returns:
        bool: 削除に成功した場合はTrue、変数が存在しない場合はFalse
    """
    db_variable = db.query(Variable).filter(Variable.id == variable_id).first()
    
    if not db_variable:
        return False
    
    db.delete(db_variable)
    db.commit()
    
    return True
