# test_error_workflow_status.py

        workflow_run_data = WorkflowRunCreate(
            type="TEST",  # 添加测试类型标识
            user_id="1024",
            app_id="test-app-error",
            workflow_run_id="test-workflow-error-456",
            status="RUNNING",
            params="{}",
            memory="",
            ip_addr="127.0.0.1",
            flow_json=json.dumps({
                "nodes": [
                    {
                        "id": "start-node-error",
                        "type": "startNode",
                        "data": {"label": "错误开始节点"}
                    }
                ],
                "edges": []
            })
        )