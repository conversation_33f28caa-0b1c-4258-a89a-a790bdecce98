import httpx
import os

BASE_URL = "http://localhost:8000/kb"
TOKEN = os.getenv("TEST_TOKEN", "mxi4wdni8fnftn8n8eq7yn7xco")
HEADERS = {"Authorization": f"Bearer {TOKEN}"}

# 1. 创建知识库
resp = httpx.post(BASE_URL, json={
    "kb_name": "测试知识库",
    "kb_description": "API自动化测试"
}, headers=HEADERS)
print("Create KB:", resp.status_code, resp.json())
kb_id = resp.json().get("kb_id")

# 2. 列表知识库
resp = httpx.get(BASE_URL, headers=HEADERS)
print("List KB:", resp.status_code, resp.json())

# 3. 更新知识库
resp = httpx.patch(f"{BASE_URL}/{kb_id}", json={"kb_name": "新名称"}, headers=HEADERS)
print("Update KB:", resp.status_code, resp.json())

# 4. 上传文件（批量）
files = [
    ("files", ("test1.txt", open("test1.txt", "rb"), "text/plain")),
    ("files", ("test2.txt", open("test2.txt", "rb"), "text/plain")),
]
resp = httpx.post(f"{BASE_URL}/{kb_id}/files", files=files, headers=HEADERS)
print("Upload Files:", resp.status_code, resp.json())

# 5. 文件列表
resp = httpx.get(f"{BASE_URL}/{kb_id}/files", headers=HEADERS)
print("List Files:", resp.status_code, resp.json())

# 6. 删除知识库
resp = httpx.delete(f"{BASE_URL}/{kb_id}", headers=HEADERS)
print("Delete KB:", resp.status_code) 