#!/usr/bin/env python3
"""
Test script to verify vector dimension handling in VectorDB
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.vector_db import VectorDB
import random

def create_mock_vector(dimension: int) -> list:
    """Create a mock vector with specified dimension"""
    return [random.random() for _ in range(dimension)]

def test_vector_dimensions():
    """Test vector dimension handling"""
    print("Testing Vector Dimension Handling...")
    
    # Create VectorDB instance
    vdb = VectorDB()
    
    # Test 1: Check dimension mapping
    print("\n1. Testing dimension mapping:")
    models_and_dimensions = [
        ("text-embedding-3-small", 1536),
        ("text-embedding-3-large", 3072),
        ("text-embedding-ada-002", 1536),
        ("openai://text-embedding-3-large", 3072),
    ]
    
    for model, expected_dim in models_and_dimensions:
        actual_dim = vdb._get_embedding_dimension(model)
        print(f"   {model}: expected={expected_dim}, actual={actual_dim}, {'✓' if actual_dim == expected_dim else '✗'}")
    
    # Test 2: Test with 1536-dimensional vectors (text-embedding-3-small)
    print("\n2. Testing 1536-dimensional vectors:")
    vectors_1536 = [
        {
            "vector": create_mock_vector(1536),
            "kb_id": "test-kb-1536",
            "file_id": "test-file-1",
            "chunk_id": "chunk-1",
            "text": "Test text for 1536-dim vector",
            "metadata": {"test": True}
        }
    ]
    
    try:
        success = vdb.add_vectors(vectors_1536, "text-embedding-3-small")
        print(f"   Added 1536-dim vectors: {'✓' if success else '✗'}")
    except Exception as e:
        print(f"   Error adding 1536-dim vectors: {e}")
    
    # Test 3: Test with 3072-dimensional vectors (text-embedding-3-large)
    print("\n3. Testing 3072-dimensional vectors:")
    vectors_3072 = [
        {
            "vector": create_mock_vector(3072),
            "kb_id": "test-kb-3072",
            "file_id": "test-file-2",
            "chunk_id": "chunk-2",
            "text": "Test text for 3072-dim vector",
            "metadata": {"test": True}
        }
    ]
    
    try:
        success = vdb.add_vectors(vectors_3072, "text-embedding-3-large")
        print(f"   Added 3072-dim vectors: {'✓' if success else '✗'}")
    except Exception as e:
        print(f"   Error adding 3072-dim vectors: {e}")
    
    # Test 4: Test search with different dimensions
    print("\n4. Testing search functionality:")
    
    # Search with 1536-dim query vector
    try:
        query_vector_1536 = create_mock_vector(1536)
        results = vdb.search_vectors(query_vector_1536, "test-kb-1536", limit=5, embedding_model="text-embedding-3-small")
        print(f"   Search with 1536-dim vector: {'✓' if isinstance(results, list) else '✗'}")
    except Exception as e:
        print(f"   Error searching with 1536-dim vector: {e}")
    
    # Search with 3072-dim query vector
    try:
        query_vector_3072 = create_mock_vector(3072)
        results = vdb.search_vectors(query_vector_3072, "test-kb-3072", limit=5, embedding_model="text-embedding-3-large")
        print(f"   Search with 3072-dim vector: {'✓' if isinstance(results, list) else '✗'}")
    except Exception as e:
        print(f"   Error searching with 3072-dim vector: {e}")
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_vector_dimensions()
