# utils/auth.py

import jwt
import os
from datetime import datetime, timedelta
from typing import Tuple
from fastapi import Header, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordBearer
import requests
from sqlalchemy.orm import Session
from sqlalchemy import select
from models.user_info import UserInfo

from models.tokens import Tokens
from models.api_keys import APIKey
import secrets
from database.db import get_db


from dotenv import load_dotenv  # dotenvが必要

load_dotenv()  # .envファイルを読み込み

SECRET_KEY = os.getenv("__SECRET_KEY")   # 本番環境ではより強力なキーを使用してください
ALGORITHM = os.getenv("__ALGORITHM")

# 環境変数から取得し、整数に変換
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("__ACCESS_TOKEN_EXPIRE_MINUTES", "120").split()[0]) # 2時間
REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("__REFRESH_TOKEN_EXPIRE_DAYS", "30").split()[0])  # 1ヶ月


def create_tokens(user_id: str) -> Tuple[str, str]:
    access_token_expiration = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    refresh_token_expiration = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)

    access_token = jwt.encode(
        {"sub": user_id, "exp": access_token_expiration},
        SECRET_KEY,
        algorithm=ALGORITHM
    )

    refresh_token = jwt.encode(
        {"sub": user_id, "exp": refresh_token_expiration},
        SECRET_KEY,
        algorithm=ALGORITHM
    )

    return access_token, refresh_token

oauth2_scheme = OAuth2PasswordBearer(tokenUrl = "/users/login")

def verify_token(token: str) -> str:
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload["sub"]
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

async def verify_auth(
    token: str = Depends(oauth2_scheme),
    request: Request = None
) -> str:
    """Verify authentication using either Bearer token or API key"""
    user_id = verify_token(token)
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token"
        )
    return user_id

def get_current_user(token: str = Depends(oauth2_scheme)):
    user_id = verify_token(token)
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
        )
    return user_id

def get_current_company_id(user_id: str, db: Session) -> str:
    query = select(UserInfo.company_id).where(
        UserInfo.user_id == user_id,
        UserInfo.is_delete != "1"  # 削除されていないレコード
    )
    result = db.execute(query).scalar()

    if not result:
        raise ValueError("Company ID not found for the given user ID.")

    return result

def generate_api_key() -> str:
    """Generate secure 128-bit API key"""
    return secrets.token_urlsafe(24)  # 24 bytes = 192 bits -> 32 char URL-safe string

async def verify_api_key(
    # Assume the API key is passed in a header named "X-API-Key".
    # Adjust `alias="X-API-Key"` if you use a different header name.
    # The `...` indicates that this header is required.
    x_api_key: str = Header(..., alias="X-API-Key", description="Your API Key"),
    db: Session = Depends(get_db)
) -> Tuple[str, str]:
    """
    验证API密钥有效性并返回关联的user_id和app_id

    Args:
        x_api_key (str): 要验证的API密钥 (从 "X-API-Key" header 读取)
        db (Session): データベース会话

    Returns:
        tuple: (user_id, app_id) 关联のユーザーIDと应用ID

    Raises:
        HTTPException: 当密钥无效或过期時
    """
    # 查询数据库获取密钥记录
    api_key_entry = db.query(APIKey).filter(
        APIKey.api_key == x_api_key  # Use the API key from the header
    ).first()

    if not api_key_entry:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )

    # 检查有效期
    # Ensure api_key_entry.expiration is timezone-aware (UTC) or compare naive UTC datetimes consistently.
    # Using datetime.now(timezone.utc) for a timezone-aware comparison.
    if api_key_entry.expiration and api_key_entry.expiration < datetime.now(timezone.utc):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="API key has expired"
        )

    # 修改返回值为元組，包含user_id和app_id
    return api_key_entry.user_id, api_key_entry.app_id
