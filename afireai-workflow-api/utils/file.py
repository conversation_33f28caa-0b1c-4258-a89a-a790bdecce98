# utils/file.py

def get_file_type(mime_type: str) -> str:
    """
    MIME タイプからファイルの種別を判定して返す関数。
    以下のようなファイル形式に対応しています：
      - "text": テキストファイル (例: text/plain, text/csv, text/html, application/json, application/xml)
      - "pdf": PDF ファイル (application/pdf)
      - "excel": Excel ファイル (application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet)
      - "word": Word ファイル (application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document)
      - "powerpoint": PowerPoint ファイル (application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.presentationml.presentation)
      - "image": 画像ファイル (image/jpeg, image/png, image/gif, image/bmp, image/tiff, image/webp)
      - "audio": 音声ファイル (audio/mpeg, audio/wav, audio/ogg)
      - "video": 動画ファイル (video/mp4, video/x-msvideo, video/mpeg, video/webm)
      - "archive": 圧縮ファイル (application/zip, application/x-rar-compressed, application/x-7z-compressed, application/x-tar, application/gzip)
      - "unknown": 上記に該当しない場合
    """
    # テキストファイル：text/で始まるものや、特定の application 形式
    if mime_type.startswith("text/"):
        return "text"
    elif mime_type in ("application/json", "application/javascript", "application/xml"):
        return "text"

    # PDF ファイル
    elif mime_type == "application/pdf":
        return "pdf"
    
    # Excel ファイル
    elif mime_type in (
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    ):
        return "excel"
    
    # Word ファイル
    elif mime_type in (
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ):
        return "word"
    
    # PowerPoint ファイル
    elif mime_type in (
        "application/vnd.ms-powerpoint",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation"
    ):
        return "powerpoint"
    
    # 画像ファイル
    elif mime_type.startswith("image/"):
        return "image"
    
    # 音声ファイル
    elif mime_type.startswith("audio/"):
        return "audio"
    
    # 動画ファイル
    elif mime_type.startswith("video/"):
        return "video"
    
    # 圧縮ファイル
    elif mime_type in (
        "application/zip",
        "application/x-rar-compressed",
        "application/x-7z-compressed",
        "application/x-tar",
        "application/gzip"
    ):
        return "archive"
    
    # 該当しない場合は unknown を返す
    else:
        return "unknown"
