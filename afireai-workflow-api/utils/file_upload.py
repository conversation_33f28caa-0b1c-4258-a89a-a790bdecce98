import os, uuid, shutil
from pathlib import Path
from fastapi import UploadFile

BASE_DIR = Path(os.getenv("__KNOWLEDGE_UPLOAD_PATH", "./uploads")).expanduser()
BASE_DIR.mkdir(parents=True, exist_ok=True)

def save_to_kb(kb_id: str, f: UploadFile) -> str:
    "保存文件到 ./<BASE>/<kb_id>/<uuid>_<filename>; 返回保存路径字符串"
    kb_dir = BASE_DIR / kb_id
    kb_dir.mkdir(parents=True, exist_ok=True)
    dest = kb_dir / f"{uuid.uuid4().hex}_{f.filename}"
    with dest.open("wb") as out:
        shutil.copyfileobj(f.file, out)
    return str(dest) 