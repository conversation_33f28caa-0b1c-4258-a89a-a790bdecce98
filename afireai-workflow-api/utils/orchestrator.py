# utils/orchestrator.py

import asyncio
import json
import logging
import uuid
import os
from datetime import datetime, timezone
from typing import Dict, List, Set, Tuple, Any, Optional
from sqlalchemy.orm import Session
import httpx  # 非同期 HTTP クライアント

from models.workflow_runs import WorkflowRun
from models.workflow_run_nodes import WorkflowRunNode
from models.variables import Variable
from schemas.workflow_runs import WorkflowRunUpdate
from schemas.workflow_run_nodes import WorkflowRunNodeCreate, WorkflowRunNodeUpdate
from schemas.variables import VariableCreate, VariableUpdate
from service.workflow_runs_service import update_workflow_run, get_workflow_run
from service.workflow_run_nodes_service import create_workflow_run_node, update_workflow_run_node
from service.variables_service import create_variable, update_variable_by_key, get_variable_by_key, get_variables_dict_by_workflow_run
from utils.auth import create_tokens

logger = logging.getLogger(__name__)

# API ベース URL (環境変数等で設定するのがおすすめ)
API_BASE_URL = os.getenv("API_BASE_URL", "http://127.0.0.1:8000")

class Orchestrator:
    """ワークフローの実行を管理するオーケストレーター"""

    def __init__(self, db: Session, api_base_url: str = API_BASE_URL):
        """
        オーケストレーターの初期化

        Args:
            db (Session): データベースセッション
            api_base_url (str): 外部 API のベース URL
        """
        self.db = db
        self.api_base_url = api_base_url
        self.node_outputs: Dict[str, Any] = {}  # ノードの出力を保存
        self.completed_nodes: Set[str] = set()  # 完了したノードのID
        self.failed_nodes: Set[str] = set()  # 失敗したノードのID
        self.variables: Dict[str, Any] = {}  # ワークフロー変数
        self.user_id: Optional[str] = None  # ユーザーID（run_workflow メソッドで設定）
        self.auth_token: Optional[str] = None  # 認証トークン
        # 非同期排他制御用ロック
        self._exec_lock = asyncio.Lock()
        # ノードマッピング設定を読み込み
        self.endpoint_map = self._load_node_mapping()

    def _load_node_mapping(self) -> Dict[str, str]:
        """
        nodeMap.json ファイルからノードタイプとAPIエンドポイントのマッピングを読み込む

        Returns:
            Dict[str, str]: ノードタイプとエンドポイントのマッピング
        """
        try:
            # プロジェクトルートのnodeMap.jsonファイルを読み込み
            node_map_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "nodeMap.json")

            with open(node_map_path, 'r', encoding='utf-8') as f:
                node_config = json.load(f)
                endpoint_map = node_config.get("endpoint_map", {})
                logger.info(f"Loaded node mapping from {node_map_path}: {endpoint_map}")
                return endpoint_map
        except FileNotFoundError:
            logger.warning(f"nodeMap.json not found at {node_map_path}, using default mapping")
            # フォールバック用のデフォルトマッピング
            return {
                'startNode': 'run/v1/startNode',
                'endNode': 'run/v1/endNode',
                'llmNode': 'run/v1/llmNode',
                'datasetGrepNode': 'run/v1/datasetGrepNode',
                'httpRequestNode': 'run/v1/httpRequestNode',
            }
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing nodeMap.json: {e}, using default mapping")
            # JSONパースエラーの場合もデフォルトマッピングを使用
            return {
                'startNode': 'run/v1/startNode',
                'endNode': 'run/v1/endNode',
                'llmNode': 'run/v1/llmNode',
                'datasetGrepNode': 'run/v1/datasetGrepNode',
                'httpRequestNode': 'run/v1/httpRequestNode',
            }
        except Exception as e:
            logger.error(f"Unexpected error loading nodeMap.json: {e}, using default mapping")
            return {
                'startNode': 'run/v1/startNode',
                'endNode': 'run/v1/endNode',
                'llmNode': 'run/v1/llmNode',
                'datasetGrepNode': 'run/v1/datasetGrepNode',
                'httpRequestNode': 'run/v1/httpRequestNode',
            }

    async def run_workflow(self, app_id: str, workflow_run_id: str, flow_json_str: str) -> None:
        """
        ワークフローを実行する

        Args:
            app_id (str): アプリID
            workflow_run_id (str): ワークフロー実行ID
            flow_json_str (str): ワークフローのJSON文字列
        """
        try:
            # JSONをパース
            flow_json = json.loads(flow_json_str)
            logger.debug(f"Parsed flow_json: {flow_json}")

            # 実行ステータスを「RUNNING」に更新
            workflow_run = get_workflow_run(self.db, workflow_run_id)
            if not workflow_run:
                logger.error(f"Workflow run id {workflow_run_id} not found")
                return

            self.user_id = workflow_run.user_id

            # 内部API呼び出し用の認証トークンを生成
            access_token, _ = create_tokens(str(self.user_id))
            self.auth_token = access_token
            logger.debug(f"Generated auth token for user {self.user_id}")

            update_data = WorkflowRunUpdate(
                status="RUNNING",
                started_at=datetime.now(timezone.utc)
            )
            update_workflow_run(self.db, workflow_run_id, update_data)

            # 変数の初期化
            self.variables = get_variables_dict_by_workflow_run(self.db, workflow_run_id)

            # ノードとエッジの抽出
            nodes = flow_json.get("nodes", [])
            edges = flow_json.get("edges", [])

            # 依存関係を構築
            parents, children = self._build_dependencies(nodes, edges)

            # 実行キュー初期化
            queue = list(parents.keys())
            if not queue:
                logger.error("No nodes found in the queue")
                return

            # startノードを見つけ実行
            start_nodes = [nid for nid in queue if nid.startswith('start-')]
            if not start_nodes:
                logger.error("No start node found")
                return

            # type/data 辞書を準備
            id_to_type = {n['id']: n['type'] for n in nodes}
            id_to_data = {n['id']: n.get('data', {}) for n in nodes}

            await self._execute_node(workflow_run_id,start_nodes[0], children, id_to_type, id_to_data)

            # ステータスの最終更新
            status = "FAILED" if self.failed_nodes else "SUCCESS"
            update_data = WorkflowRunUpdate(
                status=status,
                finished_at=datetime.now(timezone.utc)
            )
            update_workflow_run(self.db, workflow_run_id, update_data)

        except Exception as e:
            logger.exception(f"Error running workflow: {e}")
            update_data = WorkflowRunUpdate(
                status="FAILED",
                finished_at=datetime.now(timezone.utc)
            )
            update_workflow_run(self.db, workflow_run_id, update_data)

    def _build_dependencies(
        self,
        nodes: List[Dict],
        edges: List[Dict]
    ) -> Tuple[Dict[str, Set[str]], Dict[str, Set[str]]]:
        # 初期化
        parents = {node["id"]: set() for node in nodes}
        children = {node["id"]: set() for node in nodes}
        for edge in edges:
            src = edge.get("source")
            tgt = edge.get("target")
            if src and tgt:
                parents.setdefault(tgt, set()).add(src)
                children.setdefault(src, set()).add(tgt)
        return parents, children

    async def _execute_node(
        self,
        workflow_run_id: str,
        node_id: str,
        children: Dict[str, Set[str]],
        id_to_type: Dict[str, str],
        id_to_data: Dict[str, Dict]
    ) -> None:
        # 実行済みチェック & 登録
        async with self._exec_lock:
            if node_id in self.completed_nodes:
                logger.info(f"Node {node_id} already executed, skipping")
                return
            self.completed_nodes.add(node_id)

        # ノード処理開始
        logger.info(f"[START] Executing node {node_id}")
        await asyncio.sleep(3)  # 実際の処理に置き換え

        # ノードタイプとデータ
        node_type = id_to_type.get(node_id)
        node_data = id_to_data.get(node_id, {})

        # --- node_type に応じた API 呼び出し ---
        # JSONファイルから読み込んだエンドポイントマッピングを使用
        endpoint = self.endpoint_map.get(node_type)
        if endpoint:
            url = f"{self.api_base_url}/{endpoint}"
            try:
                # 認証ヘッダーを準備
                headers = {}
                if self.auth_token:
                    headers["Authorization"] = f"Bearer {self.auth_token}"
                    # logger.debug(f"Using auth token for API call: {self.auth_token[:20]}...")
                else:
                    logger.warning("No auth token available for API call")

                logger.info(f"Calling API: {url} with headers: {list(headers.keys())}")

                # API呼び出し用のデータを準備
                api_data = {
                    'nodeId': node_id,
                    'workflowRunId': workflow_run_id,
                    **node_data
                }

                logger.info(f"API call data: {api_data}")

                async with httpx.AsyncClient() as client:
                    resp = await client.post(
                        url,
                        json=api_data,
                        headers=headers,
                        timeout=30.0  # 30秒のタイムアウト
                    )
                    resp.raise_for_status()
                    result = resp.json()
                    self.node_outputs[node_id] = result
                    # logger.info(f"[API SUCCESS] {node_id} -> {result}")
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error calling API {url}: {e.response.status_code} - {e.response.text}")
                self.failed_nodes.add(node_id)
                return
            except httpx.RequestError as e:
                logger.error(f"Request error calling API {url}: {e}")
                self.failed_nodes.add(node_id)
                return
            except Exception as e:
                logger.error(f"Unexpected error calling API {url}: {e}")
                self.failed_nodes.add(node_id)
                return
        # -------------------------------------

        # 子ノード実行
        next_nodes = children.get(node_id, set())
        if not next_nodes:
            logger.info(f"Node {node_id} has no children, done")
            return

        tasks = [asyncio.create_task(
            self._execute_node(workflow_run_id, child, children, id_to_type, id_to_data)
        ) for child in next_nodes]
        await asyncio.gather(*tasks)
