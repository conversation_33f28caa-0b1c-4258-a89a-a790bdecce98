import os
import qdrant_client
from typing import List, Dict, Any
from qdrant_client.models import Distance, VectorParams, PointStruct
import uuid

_HOST = os.getenv("__VDB_HOST", "localhost")
_PORT = int(os.getenv("__VDB_PORT", 6333))
_COLLECTION = os.getenv("__VDB_COLLECTION", "kb_vectors")

class VectorDB:
    def __init__(self):
        self.client = qdrant_client.QdrantClient(
            host=_HOST,
            port=_PORT
        )
        # Don't create collection in __init__ - create it when needed with proper dimensions

    def _get_embedding_dimension(self, model_name: str) -> int:
        """埋め込みモデル名から次元数を取得"""
        # OpenAI embedding models and their dimensions
        model_dimensions = {
            "text-embedding-3-small": 1536,
            "text-embedding-3-large": 3072,
            "text-embedding-ada-002": 1536,
            "openai://text-embedding-3-small": 1536,
            "openai://text-embedding-3-large": 3072,
            "openai://text-embedding-ada-002": 1536,
        }

        # Remove openai:// prefix if present
        clean_model_name = model_name.replace("openai://", "")

        return model_dimensions.get(clean_model_name, 1536)  # Default to 1536

    def _ensure_collection(self, vector_dimension: int = 1536):
        """コレクションが存在しない場合は作成する、または次元が異なる場合は再作成する"""
        try:
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if _COLLECTION not in collection_names:
                # Collection doesn't exist, create it
                self.client.create_collection(
                    collection_name=_COLLECTION,
                    vectors_config=VectorParams(size=vector_dimension, distance=Distance.COSINE)
                )
                print(f"Created collection '{_COLLECTION}' with dimension {vector_dimension}")
            else:
                # Collection exists, check if dimension matches
                collection_info = self.client.get_collection(_COLLECTION)
                existing_dimension = collection_info.config.params.vectors.size

                if existing_dimension != vector_dimension:
                    print(f"Collection dimension mismatch: existing={existing_dimension}, required={vector_dimension}")
                    print(f"Recreating collection '{_COLLECTION}' with new dimension {vector_dimension}")

                    # Delete existing collection and recreate with new dimension
                    self.client.delete_collection(_COLLECTION)
                    self.client.create_collection(
                        collection_name=_COLLECTION,
                        vectors_config=VectorParams(size=vector_dimension, distance=Distance.COSINE)
                    )
                    print(f"Recreated collection '{_COLLECTION}' with dimension {vector_dimension}")

        except Exception as e:
            print(f"Error ensuring collection: {e}")
    
    def add_vectors(self, vectors: List[Dict[str, Any]], embedding_model: str = None) -> bool:
        """ベクターをコレクションに追加"""
        try:
            if not vectors:
                return True

            # Determine vector dimension from the first vector or embedding model
            vector_dimension = len(vectors[0]["vector"])
            if embedding_model:
                expected_dimension = self._get_embedding_dimension(embedding_model)
                if vector_dimension != expected_dimension:
                    print(f"Warning: Vector dimension {vector_dimension} doesn't match expected dimension {expected_dimension} for model {embedding_model}")

            # Ensure collection exists with correct dimension
            self._ensure_collection(vector_dimension)

            points = []
            for vector_data in vectors:
                # Validate vector dimension consistency
                if len(vector_data["vector"]) != vector_dimension:
                    print(f"Skipping vector with inconsistent dimension: expected {vector_dimension}, got {len(vector_data['vector'])}")
                    continue

                point = PointStruct(
                    id=str(uuid.uuid4()),
                    vector=vector_data["vector"],
                    payload={
                        "kb_id": vector_data["kb_id"],
                        "file_id": vector_data["file_id"],
                        "chunk_id": vector_data["chunk_id"],
                        "text": vector_data["text"],
                        "metadata": vector_data.get("metadata", {}),
                        "embedding_model": embedding_model  # Store the model used
                    }
                )
                points.append(point)

            if points:
                self.client.upsert(
                    collection_name=_COLLECTION,
                    points=points
                )
                print(f"Successfully added {len(points)} vectors with dimension {vector_dimension}")

            return True
        except Exception as e:
            print(f"Error adding vectors: {e}")
            # Print more detailed error information
            if hasattr(e, 'response'):
                print(f"Response status: {e.response.status_code}")
                print(f"Response content: {e.response.content}")
            return False
    
    def search_vectors(self, query_vector: List[float], kb_id: str, limit: int = 10, embedding_model: str = None) -> List[Dict]:
        """ベクター検索"""
        try:
            # Ensure collection exists with correct dimension
            if embedding_model:
                vector_dimension = self._get_embedding_dimension(embedding_model)
                self._ensure_collection(vector_dimension)
            elif query_vector:
                vector_dimension = len(query_vector)
                self._ensure_collection(vector_dimension)

            search_result = self.client.search(
                collection_name=_COLLECTION,
                query_vector=query_vector,
                query_filter={
                    "must": [
                        {"key": "kb_id", "match": {"value": kb_id}}
                    ]
                },
                limit=limit
            )

            results = []
            for hit in search_result:
                results.append({
                    "id": hit.id,
                    "score": hit.score,
                    "payload": hit.payload
                })

            return results
        except Exception as e:
            print(f"Error searching vectors: {e}")
            return []
    
    def delete_by_kb_id(self, kb_id: str) -> bool:
        """指定されたkb_idのベクターを削除"""
        try:
            self.client.delete(
                collection_name=_COLLECTION,
                points_selector={
                    "filter": {
                        "must": [
                            {"key": "kb_id", "match": {"value": kb_id}}
                        ]
                    }
                }
            )
            return True
        except Exception as e:
            print(f"Error deleting vectors: {e}")
            return False
    
    def delete_by_file_id(self, file_id: str) -> bool:
        """指定されたfile_idのベクターを削除"""
        try:
            self.client.delete(
                collection_name=_COLLECTION,
                points_selector={
                    "filter": {
                        "must": [
                            {"key": "file_id", "match": {"value": file_id}}
                        ]
                    }
                }
            )
            return True
        except Exception as e:
            print(f"Error deleting vectors: {e}")
            return False

# シングルトンインスタンス
vector_db = VectorDB()