import os
import qdrant_client
from typing import List, Dict, Any
from qdrant_client.models import Distance, VectorParams, PointStruct
import uuid

_HOST = os.getenv("__VDB_HOST", "localhost")
_PORT = int(os.getenv("__VDB_PORT", 6333))
_COLLECTION = os.getenv("__VDB_COLLECTION", "kb_vectors")

class VectorDB:
    def __init__(self):
        self.client = qdrant_client.QdrantClient(
            host=_HOST,
            port=_PORT
        )
        self._ensure_collection()
    
    def _ensure_collection(self):
        """コレクションが存在しない場合は作成する"""
        try:
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if _COLLECTION not in collection_names:
                self.client.create_collection(
                    collection_name=_COLLECTION,
                    vectors_config=VectorParams(size=1536, distance=Distance.COSINE)
                )
        except Exception as e:
            print(f"Error ensuring collection: {e}")
    
    def add_vectors(self, vectors: List[Dict[str, Any]]) -> bool:
        """ベクターをコレクションに追加"""
        try:
            points = []
            for vector_data in vectors:
                point = PointStruct(
                    id=str(uuid.uuid4()),
                    vector=vector_data["vector"],
                    payload={
                        "kb_id": vector_data["kb_id"],
                        "file_id": vector_data["file_id"],
                        "chunk_id": vector_data["chunk_id"],
                        "text": vector_data["text"],
                        "metadata": vector_data.get("metadata", {})
                    }
                )
                points.append(point)
            
            self.client.upsert(
                collection_name=_COLLECTION,
                points=points
            )
            return True
        except Exception as e:
            print(f"Error adding vectors: {e}")
            return False
    
    def search_vectors(self, query_vector: List[float], kb_id: str, limit: int = 10) -> List[Dict]:
        """ベクター検索"""
        try:
            search_result = self.client.search(
                collection_name=_COLLECTION,
                query_vector=query_vector,
                query_filter={
                    "must": [
                        {"key": "kb_id", "match": {"value": kb_id}}
                    ]
                },
                limit=limit
            )
            
            results = []
            for hit in search_result:
                results.append({
                    "id": hit.id,
                    "score": hit.score,
                    "payload": hit.payload
                })
            
            return results
        except Exception as e:
            print(f"Error searching vectors: {e}")
            return []
    
    def delete_by_kb_id(self, kb_id: str) -> bool:
        """指定されたkb_idのベクターを削除"""
        try:
            self.client.delete(
                collection_name=_COLLECTION,
                points_selector={
                    "filter": {
                        "must": [
                            {"key": "kb_id", "match": {"value": kb_id}}
                        ]
                    }
                }
            )
            return True
        except Exception as e:
            print(f"Error deleting vectors: {e}")
            return False
    
    def delete_by_file_id(self, file_id: str) -> bool:
        """指定されたfile_idのベクターを削除"""
        try:
            self.client.delete(
                collection_name=_COLLECTION,
                points_selector={
                    "filter": {
                        "must": [
                            {"key": "file_id", "match": {"value": file_id}}
                        ]
                    }
                }
            )
            return True
        except Exception as e:
            print(f"Error deleting vectors: {e}")
            return False

# シングルトンインスタンス
vector_db = VectorDB()