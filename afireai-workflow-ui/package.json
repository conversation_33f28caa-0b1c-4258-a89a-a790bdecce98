{"name": "my-react-flow-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-icons": "^1.3.2", "@xyflow/react": "^12.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^6.30.0", "react-toastify": "^11.0.5"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}