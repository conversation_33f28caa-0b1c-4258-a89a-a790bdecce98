/* 节点执行按钮样式 */

.node-execute-button {
    position: absolute;
    top: -24px;
    right: 0px;
    z-index: 20;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    pointer-events: none;
    /* 默认不响应鼠标事件 */
}


/* 当父节点悬浮时显示按钮 */

.react-flow__node:hover .node-execute-button {
    opacity: 1;
    pointer-events: auto;
    /* 悬浮时响应鼠标事件 */
}

.execute-btn {
    width: 24px;
    height: 24px;
    border-radius: 30%;
    border: 1px solid #e0e0e0;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease-in-out;
}

.execute-btn:hover {
    background: #f5f5f5;
    border-color: #4a90e2;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transform: scale(1.05);
}

.execute-btn:active {
    transform: scale(0.95);
}

.execute-btn.disabled {
    cursor: not-allowed;
    opacity: 0.5;
    background: #f0f0f0;
}

.execute-btn.disabled:hover {
    background: #f0f0f0;
    border-color: #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: none;
}

.execute-icon {
    width: 10px;
    height: 10px;
    color: #4a90e2;
    margin-left: 1px;
    /* 微调播放图标位置 */
}

.execute-btn.disabled .execute-icon {
    color: #999;
}


/* 确保按钮在状态指示器之上 */

.node-execute-button {
    z-index: 25;
}

.node-status-indicator {
    z-index: 10;
}