import React from 'react';
import { GrPlay } from 'react-icons/gr';
import './NodeExecuteButton.css';

interface NodeExecuteButtonProps {
  nodeId: string;
  onExecute: (nodeId: string) => void | Promise<void>;
  disabled?: boolean;
}

const NodeExecuteButton: React.FC<NodeExecuteButtonProps> = ({
  nodeId,
  onExecute,
  disabled = false
}) => {
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // 防止触发节点点击事件
    if (!disabled) {
      onExecute(nodeId);
    }
  };

  return (
    <div className="node-execute-button">
      <button
        className={`execute-btn ${disabled ? 'disabled' : ''}`}
        onClick={handleClick}
        disabled={disabled}
        title="执行此节点"
      >
        <GrPlay className="execute-icon" />
      </button>
    </div>
  );
};

export default NodeExecuteButton;
