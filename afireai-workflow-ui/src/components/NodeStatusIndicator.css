/* 节点状态指示器样式 */

.node-status-indicator {
    position: absolute;
    top: 4px;
    right: 4px;
    z-index: 10;
    pointer-events: none;
    /* 不干扰节点交互 */
}

.status-icon {
    width: 12px;
    height: 12px;
}


/* 执行中状态的闪烁动画 */

.status-icon.running {
    color: #4caf50;
    animation: blink-icon 0.8s infinite;
}


/* 成功状态 */

.status-icon.success {
    color: #4caf50;
    /* 绿色 */
}


/* 失败状态 */

.status-icon.failed {
    color: #f44336;
    /* 红色 */
}

@keyframes blink-icon {
    0%,
    50% {
        opacity: 1;
    }
    51%,
    100% {
        opacity: 0.3;
    }
}