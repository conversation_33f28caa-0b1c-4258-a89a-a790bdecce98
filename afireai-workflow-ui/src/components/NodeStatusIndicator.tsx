import React from 'react';
import { GrPlay } from 'react-icons/gr';
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import './NodeStatusIndicator.css';

interface NodeStatusIndicatorProps {
  status?: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED';
}

const NodeStatusIndicator: React.FC<NodeStatusIndicatorProps> = ({ status }) => {
  // 根据状态显示不同的图标
  if (status === 'RUNNING') {
    return (
      <div className="node-status-indicator">
        <GrPlay className="status-icon running" />
      </div>
    );
  }

  if (status === 'SUCCESS') {
    return (
      <div className="node-status-indicator">
        <FaCheckCircle className="status-icon success" />
      </div>
    );
  }

  if (status === 'FAILED') {
    return (
      <div className="node-status-indicator">
        <FaTimesCircle className="status-icon failed" />
      </div>
    );
  }

  // PENDING状态或其他状态不显示图标
  return null;
};

export default NodeStatusIndicator;
