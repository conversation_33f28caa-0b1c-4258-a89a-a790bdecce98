import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { FaPlus, FaTimes,FaTools, FaRobot, FaSignOutAlt, FaFileAlt, FaClock, FaStop, FaCode, FaGlobe } from 'react-icons/fa';
import { FcPlus, FcNeutralDecision, FcMindMap, FcDisclaimer } from "react-icons/fc";
import { GrHome, GrPlay } from "react-icons/gr";
import { IoBookOutline } from 'react-icons/io5';
import { useReactFlow } from '@xyflow/react';

import './css/addNode.css';

interface AddNodeProps {
  onClick?: () => void;
}

const AddNode: React.FC<AddNodeProps> = () => {
  const reactFlowInstance = useReactFlow();
  const { addNodes } = reactFlowInstance;
  const [open, setOpen] = useState(false);
  const [menuPosition, setMenuPosition] = useState<{ top: number; left: number } | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleButtonClick = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();

    // 计算菜单位置 - 将菜单放在按钮正上方
    // 计算按钮的中心位置
    const buttonCenterX = rect.left + rect.width / 2;

    // 将菜单左侧与按钮中心对齐，并向左偏移菜单宽度的一半（假设菜单宽度为200px）
    const idealLeft = buttonCenterX - 100 + window.scrollX;

    // 将菜单底部放在按钮正上方，CSS中的transform会将菜单向上移动
    const idealTop = rect.top + window.scrollY;

    setMenuPosition({
      top: idealTop,
      left: idealLeft
    });

    setOpen(!open); // Toggle the menu
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // Check if the click target is NOT inside the menu AND NOT inside the button
      if (
        menuRef.current && !menuRef.current.contains(target) &&
        buttonRef.current && !buttonRef.current.contains(target)
      ) {
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside); // Corrected line
    };
  }, [open]);

  return (
    <div className="addNodeContainer">
      <button
        ref={buttonRef}
        className={`addNodeButton ${open ? 'open' : ''}`}
        onClick={handleButtonClick}
      >
        <div className="addNodeContent">
          <FcPlus className="addNodeIcon" />
          <span>Add node</span>
        </div>
      </button>

      {open && menuPosition && createPortal(
        <div
          ref={menuRef}
          className="menu"
          style={{
            top: menuPosition.top,
            left: menuPosition.left
          }}
        >
          {/* 添加关闭按钮 */}
          <button
            className="closeButton" // 使用CSS类来定位和样式化
            onClick={() => setOpen(false)} // 点击时关闭菜单
            aria-label="Close menu" // 添加无障碍标签
          >
            <FaTimes style={{ fontSize: '14px' }} />
          </button>

          {/* start menu items */}
          <button className="addNode-menuItem"
            onClick={() => {
              // 添加开始节点
              const newNode = {
                id: `start-${Date.now()}`,
                type: 'startNode',
                position: { x: 250, y: 250 },
                data: { label: '開始' }
              };

              // 添加节点
              addNodes(newNode);

              console.log('Start Node added with zoom level 0.5');
              setOpen(false);
            }}
          >
            <div className="icon-container" style={{ backgroundColor: '#38a507',color:'#ffffff' }}>
              <GrPlay/>
            </div>
            開始
          </button>

          {/* LLM menu items */}
          <button className="addNode-menuItem"
            onClick={() => {
              // 添加LLM节点
              const newNode = {
                id: `llm-${Date.now()}`,
                type: 'llmNode',
                position: { x: 250, y: 250 },
                data: { label: 'LLM', model: 'gpt-3.5-turbo' }
              };

              // 添加节点
              addNodes(newNode);
              console.log('LLM Node added');
              setOpen(false);
            }}
          >
            <div className="icon-container" style={{ backgroundColor: '#5468ff',color:'#ffffff' }}>
              <FaRobot />
            </div>
            LLM
          </button>

          {/* ナレッジ検索 menu items */}
          <button className="addNode-menuItem"
            onClick={() => {
              // 添加知识检索节点
              const newNode = {
                id: `dataset-grep-${Date.now()}`,
                type: 'datasetGrepNode',
                position: { x: 250, y: 250 },
                data: { label: '知識検索', dataset: '社内業務まとめ資料.pdf...' }
              };

              // 添加节点
              addNodes(newNode);
              console.log('Dataset Grep Node added');
              setOpen(false);
            }}
          >
            <div className="icon-container" style={{ backgroundColor: '#10b981' }}>
              <IoBookOutline style={{ fontSize: '14px', color: 'white' }} />
            </div>
            ナレッジ検索
          </button>

          {/* コード実行 menu items */}
          <button className="addNode-menuItem"
            onClick={() => {
              // 添加代码执行节点
              const newNode = {
                id: `code-${Date.now()}`,
                type: 'codeNode',
                position: { x: 250, y: 250 },
                data: { label: 'コード実行', language: 'python3' }
              };

              // 添加节点
              addNodes(newNode);
              console.log('Code Node added');
              setOpen(false);
            }}
          >
            <div className="icon-container" style={{ backgroundColor: '#3b82f6' }}>
              <FaCode style={{ fontSize: '14px', color: 'white' }} />
            </div>
            コード実行
          </button>

          {/* HTTP Request menu items */}
          <button className="addNode-menuItem"
            onClick={() => {
              // 添加HTTP请求节点
              const newNode = {
                id: `http-request-${Date.now()}`,
                type: 'httpRequestNode',
                position: { x: 250, y: 250 },
                data: { label: 'HTTPリクエスト', method: 'GET' }
              };

              // 添加节点
              addNodes(newNode);
              console.log('HTTP Request Node added');
              setOpen(false);
            }}
          >
            <div className="icon-container" style={{ backgroundColor: '#8b5cf6' }}>
              <FaGlobe style={{ fontSize: '14px', color: 'white' }} />
            </div>
            HTTPリクエスト
          </button>

          {/* Wait menu items */}
          <button className="addNode-menuItem"
            onClick={() => {
              // 添加Wait节点
              const newNode = {
                id: `wait-${Date.now()}`,
                type: 'waitNode',
                position: { x: 250, y: 250 },
                data: { label: '承認待ち' }
              };

              // 添加节点
              addNodes(newNode);
              console.log('Wait Node added');
              setOpen(false);
            }}
          >
            <div className="icon-container" style={{ backgroundColor: '#f39c12' }}>
              <FaClock style={{ fontSize: '14px', color: 'white' }} />
            </div>
            承認待ち
          </button>

          {/* End menu items */}
          <button className="addNode-menuItem"
            onClick={() => {
              // 添加终止节点
              const newNode = {
                id: `end-${Date.now()}`,
                type: 'endNode',
                position: { x: 400, y: 250 },
                data: { label: '終了' }
              };

              // 添加节点
              addNodes(newNode);

              // // 设置视图缩放比例为0.5（50%）
              // setTimeout(() => {
              //   reactFlowInstance.zoomTo(0.5);
              //   reactFlowInstance.fitView({ padding: 0.5, includeHiddenNodes: true });
              // }, 100);

              console.log('End Node added with zoom level 0.5');
              setOpen(false);
            }}
          >
            <div className="icon-container">
              <FaStop style={{ fontSize: '14px', color: '#e74c3c' }} />
            </div>
            終了
          </button>
          </div>,
        document.body
      )}
    </div>
  );
};

export default AddNode;