import React from 'react';

// GPT
export const GptIcon: React.FC<{}> = ({}) => {
  return (
    <svg t="1748863452481" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7358" width="20" height="20"><path d="M990.287157 498.421145c-26.731572-58.390074-37.949286-110.813278-36.687719-175.187268C956.395342 181.409929 824.868505 71.142193 680.777147 79.529906c-18.753016 1.091085-42.109046-5.983917-56.53182-17.713077C489.922585-46.916569 301.352482-8.200114 223.885477 146.478174c-10.757412 21.46368-25.026753 33.295128-47.308746 41.682841C18.454707 247.522835-41.350367 431.16601 51.170196 572.648994 64.007488 592.271468 68.746887 610.342556 65.337248 634.278225 40.36164 812.43188 165.42721 946.85691 344.927673 938.128233c15.104702-0.733072 33.636092 6.103254 45.638022 15.650245 50.377421 40.114407 106.159121 63.692063 171.044557 63.964834 99.83424-0.119337 187.206247-54.554229 231.003065-146.017804 10.228918-21.497776 23.867475-34.096393 46.729107-42.006757 136.573103-47.615613 210.749807-200.810708 150.944733-331.297606zM815.355611 190.735292c58.202543 43.660432 78.285319 103.380264 72.335499 179.159499-11.047231-4.892832-18.974643-7.603496-26.134886-11.729159-52.84941-30.550368-106.295506-60.418809-158.241361-92.55466-29.322898-18.105185-54.673567-16.945907-83.808935 0.66488-70.988691 42.961456-143.307141 83.75779-220.569568 128.645692 0-33.192839-1.534338-59.123146 0.869458-84.71249 0.818313-9.103737 9.103737-20.61127 17.269823-25.452957 66.334533-39.313141 132.276958-79.59803 200.691371-115.058279C686.624678 134.084135 753.91391 144.654017 815.355611 190.735292z m-309.13495 199.463901l109.85858 60.69158v127.401174l-107.403639 59.191339-109.364182-60.674532v-121.928703z m-241.811621-131.543885c2.74476-80.126524 41.290732-139.931598 115.382195-172.067449C447.574865 57.196768 512.238675 67.596168 576.135316 113.728588c-60.043749 35.119285-115.552677 70.71592-174.04504 100.58436-38.153864 19.520185-53.002843 44.223022-51.229831 87.303815 3.290302 80.126524 0.937651 160.338289 0.937651 247.897828-29.408139-17.781269-55.96923-32.272236-80.433392-49.712542-6.035062-4.313194-7.27958-18.156329-7.27958-27.635126-0.47735-71.142125-2.113976-142.403587 0.323916-213.511615z m-186.984621 139.113284c10.77446-67.749533 51.911759-112.518098 119.627196-142.778647 0 72.778752 1.70482 139.795213-0.613735 206.760529-1.193374 37.898141 13.04187 62.089532 45.723263 80.126525 71.875197 39.671154 142.642261 81.268753 219.546677 125.338342C432.470163 683.66685 408.006 698.379444 382.433705 711.165591c-5.660001 2.778856-16.008257 1.176326-21.975125-2.250361-70.289715-40.523563-141.926237-79.103632-209.692819-123.599426C84.090264 541.536035 65.337248 473.820598 77.424419 397.768592z m49.712542 286.529042c0.119337-8.677532 2.318555-17.338016 4.108615-29.885489 61.799713 35.971695 121.314967 68.755377 178.682149 104.727072 31.249344 19.588378 57.605856 19.09398 88.650623-0.136386 61.203026-37.8129 124.12792-72.881041 186.456126-108.937976 9.188978-5.319037 18.90645-9.700424 32.52796-16.63904 0 30.465127 1.500241 56.259049-0.784217 81.729054-0.750121 9.069641-8.69458 20.679463-16.758378 25.470006-67.033509 39.824587-133.078223 81.831344-202.788299 116.422135-125.338342 62.345255-272.003978-32.90302-270.094579-172.749376zM750.197404 776.801149c-2.557229 77.467006-42.722781 131.390452-113.455749 162.571603-68.82357 30.311694-132.106476 19.162173-196.054262-27.277115 46.950734-27.4135 90.66231-52.84941 134.237501-78.421704 105.323759-61.936098 89.588273-34.011152 90.628213-157.082084 0.528494-64.067123 0.085241-128.117198 0.085241-199.157034 28.044283 16.519703 51.349168 28.010187 71.875197 43.30242 7.84217 5.830483 12.956629 20.168017 13.093015 30.686754 0.920603 75.182547 2.096928 150.365094-0.409156 225.37716z m190.752272-160.883832c-9.069641 72.829896-49.780734 124.179064-122.337859 155.547746 0-74.074414-1.244518-143.204852 0.545542-212.096615 0.869458-34.096393-11.06428-55.96923-41.239588-72.574173-73.477728-40.472419-145.608648-83.348633-223.041556-127.980812 28.010187-15.871871 51.280976-30.448079 75.915619-42.177238 7.347773-3.409639 20.014583-1.89235 27.447597 2.318554 67.033509 37.966334 134.800091 74.739294 199.463901 116.422135s92.691045 104.675927 83.246344 180.608596z" fill="#13A37C" p-id="7359"></path></svg>  
  );
}

// DeepSeek
export const DeepseekIcon: React.FC<{}> = ({}) => {
  return (
    <svg t="1748863908167" class="icon" viewBox="0 0 1391 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2199" width="20" height="20"><path d="M980.736 172.527c-8.79-4.396-12.585 3.983-17.72 8.241-1.75 1.374-3.244 3.16-4.738 4.808-12.844 14.01-27.85 23.214-47.476 22.115-28.674-1.648-53.159 7.555-74.794 29.945-4.601-27.61-19.883-44.093-43.148-54.67-12.191-5.494-24.485-10.989-33.019-22.94-5.94-8.516-7.555-17.994-10.542-27.334-1.889-5.632-3.778-11.401-10.13-12.363-6.92-1.099-9.616 4.808-12.329 9.753-10.817 20.192-15.024 42.445-14.595 64.972 0.928 50.687 21.91 91.071 63.565 119.78 4.739 3.296 5.958 6.593 4.464 11.4-2.833 9.89-6.233 19.506-9.203 29.396-1.89 6.319-4.722 7.692-11.367 4.945-22.854-9.753-42.6-24.176-60.062-41.62-29.618-29.259-56.404-61.539-89.8-86.813a394.916 394.916 0 0 0-23.815-16.621c-34.083-33.791 4.464-61.538 13.393-64.835 9.323-3.434 3.245-15.247-26.923-15.11s-57.761 10.44-92.926 24.176c-5.15 2.06-10.56 3.571-16.088 4.808-31.937-6.182-65.075-7.555-99.708-3.572-65.195 7.418-117.273 38.874-155.562 92.582-45.982 64.56-56.817 137.912-43.544 214.422 13.908 80.632 54.24 147.39 116.191 199.587 64.251 54.12 138.238 80.632 222.647 75.55 51.27-3.023 108.344-10.028 172.732-65.66 16.243 8.242 33.293 11.539 61.556 14.011 21.789 2.06 42.754-1.099 58.98-4.533 25.429-5.494 23.66-29.533 14.474-33.928-74.536-35.44-58.173-21.017-73.042-32.692 37.877-45.742 94.951-93.27 117.273-247.252 1.768-12.225 0.274-19.917 0-29.807-0.138-6.044 1.201-8.38 7.984-9.066 18.664-2.198 36.796-7.418 53.434-16.758 48.282-26.923 67.77-71.154 72.372-124.176 0.687-8.104-0.137-16.483-8.534-20.741M559.911 649.722c-72.218-57.967-107.263-77.06-121.737-76.236-13.53 0.824-11.092 16.621-8.122 26.923 3.108 10.165 7.177 17.17 12.86 26.099 3.916 5.907 6.629 14.698-3.931 21.291-23.266 14.698-63.702-4.945-65.59-5.906-47.081-28.297-86.453-65.66-114.183-116.758-26.785-49.176-42.324-101.923-44.9-158.241-0.687-13.599 3.245-18.407 16.484-20.88a159.683 159.683 0 0 1 52.901-1.373c73.73 10.99 136.504 44.643 189.113 97.94 30.031 30.356 52.747 66.62 76.167 102.06 24.88 37.636 51.666 73.488 85.749 102.883 12.036 10.302 21.634 18.132 30.837 23.901-27.73 3.16-74.003 3.846-105.648-21.703m34.632-227.334c0-6.044 4.74-10.852 10.697-10.852q2.026 0.035 3.64 0.687a10.766 10.766 0 0 1 6.903 10.165 10.714 10.714 0 0 1-10.68 10.852 10.594 10.594 0 0 1-10.56-10.852m107.538 56.318c-6.886 2.885-13.788 5.358-20.433 5.632-10.268 0.55-21.497-3.708-27.575-8.928-9.478-8.105-16.244-12.638-19.077-26.786-1.219-6.044-0.55-15.384 0.533-20.741 2.438-11.539-0.275-18.956-8.242-25.687-6.49-5.495-14.75-7.006-23.815-7.006-3.383 0-6.49-1.51-8.791-2.747a9.014 9.014 0 0 1-3.915-12.637c0.944-1.923 5.546-6.594 6.628-7.418 12.31-7.143 26.51-4.807 39.628 0.55 12.174 5.082 21.36 14.423 34.633 27.61 13.53 15.934 15.968 20.33 23.66 32.28 6.096 9.34 11.642 18.956 15.42 29.944 2.317 6.869-0.67 12.5-8.654 15.934" fill="#4D6BFE" p-id="2200"></path></svg>
  );
}

// ClaudeIcon
export const ClaudeIcon: React.FC<{}> = ({}) => {
  return (
    <svg t="1748864170481" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3293" width="20" height="20"><path d="M134.741333 760.6272l1.860267-30.3616 80.571733-58.368 189.559467-108.305067 4.1984-6.075733v-7.936L78.848 536.064l-24.695467-6.075733L34.133333 502.442667v-12.1344l20.0192-13.994667 356.778667 26.129067 5.12-6.997334-288.3072-194.218666-15.8208-22.4256-4.1984-37.819734 24.2176-27.5456 47.035733 4.676267 223.095467 170.410667 8.8576-6.997334-145.783467-247.466666-12.578133-35.003734v-19.626666l31.214933-40.1408L301.482667 34.133333l39.1168 5.12 15.36 14.4896 49.373866 112.059734 95.488 187.682133 13.038934 45.294933h6.519466l27.016534-295.082666 15.36-37.341867 28.893866-19.626667 23.7568 13.090134 19.5584 26.606933-51.712 284.808533h8.3968l8.362667-7.936 106.666667-136.328533 60.552533-66.304 22.357333-18.688h41.915734l31.214933 42.9568-14.4384 47.1552-128.085333 166.690133-31.214934 54.152534 3.259734 4.676266 127.146666-26.146133 128.5632-23.808 33.0752 15.872 5.12 16.3328-12.578133 30.8224-308.3264 70.485333v4.215467l89.429333 7.4752h64.733867l116.906667 9.796267 34.474666 22.408533L989.866667 607.931733l-3.720534 19.626667-49.834666 24.2688-266.888534-63.488-10.24-3.2768v7.936l223.573334 205.909333 4.181333 24.746667-10.24 15.4112h-15.377067l-86.6304-66.304-109.448533-94.788267h-6.058667v7.936l110.848 164.352 4.6592 44.373334-7.918933 13.994666-25.156267 8.874667-25.156266-5.137067-155.5456-242.3296-5.597867 4.215467-27.477333 289.006933-13.038934 15.872-27.477333 10.734934-23.7568-18.210134-13.038933-32.682666 51.694933-268.936534 6.058667-27.067733h-6.058667l-145.322667 197.0176-66.594133 73.7792-15.837867 5.12-29.815466-15.394133 3.259733-25.685334 204.936533-263.338666v-7.458134l-253.371733 165.290667-45.636267 6.536533-19.114666-18.210133z" fill="#D97757" p-id="3294"></path></svg>
  );
}

// GeminiIcon
export const GeminiIcon: React.FC<{}> = ({}) => {
  return (
    <svg t="1748864273526" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4341" width="20" height="20"><path d="M960 512.896A477.248 477.248 0 0 0 512.896 960h-1.792A477.184 477.184 0 0 0 64 512.896v-1.792A477.184 477.184 0 0 0 511.104 64h1.792A477.248 477.248 0 0 0 960 511.104z" fill="#448AFF" p-id="4342"></path></svg>
  );
}

// LlamaIcon
export const LlamaIcon: React.FC<{}> = ({}) => {
  return (
    <svg t="1748864390803" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5668" width="20" height="20"><path d="M757.12 927.84c13.312-34.752 9.92-69.504-0.928-103.904-4.16-13.12-9.92-25.76-16.16-38.08-5.152-10.336-4.736-20 2.88-27.776 21.76-22.304 27.296-50.016 28.736-79.36 1.408-30.24-0.8-60.032-14.944-87.712a73.6 73.6 0 0 0-9.44-14.4c-9.376-11.232-10.176-26.272-0.16-36.608 36.256-37.312 37.536-96.928 14.656-140.928-19.456-37.376-48.192-62.528-91.2-68.352-7.84-1.088-16.128 0.128-24.16 0.832-16.16 1.44-23.04-1.792-29.824-16-26.24-54.848-106.88-90.336-170.88-48.448-20.288 13.248-37.088 28.64-47.168 50.784-4.928 10.976-14.016 15.744-25.376 13.76-89.696-15.616-155.424 91.36-127.456 167.264 5.44 14.72 14.944 28.224 24 41.312 7.552 10.752 8.544 24.16 1.824 34.176-7.232 10.752-15.104 21.792-18.752 33.92-12 39.584-12.48 79.68 1.28 119.04 3.456 9.792 10.24 18.752 16.896 27.008 9.088 11.264 11.456 19.936 4.224 33.824a212.064 212.064 0 0 0-24.256 85.888A111.808 111.808 0 0 0 260.416 928H209.28c-10.56-48.992-4.192-96.288 14.08-142.496a18.816 18.816 0 0 0-1.056-14.464c-29.536-53.344-31.328-109.376-17.376-166.88 3.2-13.312 10.016-25.696 14.304-38.72 1.28-4 1.344-9.984-0.8-13.248-30.4-46.784-32.544-96.864-16.8-148.416a168.096 168.096 0 0 1 57.6-84.544c6.08-4.768 6.496-10.24 5.472-17.28-6.912-49.664-8.224-99.136 10.112-146.944 5.12-13.44 12.224-26.848 21.28-37.952 24.128-29.344 61.888-27.2 85.28 2.688 21.664 27.84 28.864 60.704 34.592 94.24 0.736 4.128 1.28 8.256 2.08 13.888 60.064-35.04 119.712-35.392 179.584-0.352 4-18.592 7.52-36.8 11.936-54.848 5.216-21.12 14.176-41.024 29.248-56.832 29.344-30.752 66.816-24.928 89.984 11.744 16.8 26.656 25.024 56.48 25.952 87.456 0.864 29.504-1.216 59.104-3.296 88.608-0.416 6.688 0.512 10.88 5.376 14.816a164.8 164.8 0 0 1 46.112 56.896c28.896 59.232 31.424 118.016-4.192 176a18.656 18.656 0 0 0-0.8 15.36c27.616 57.6 27.744 116.64 9.792 176.544-3.136 10.464-9.92 19.84-14.784 29.92-1.216 2.496-2.72 5.984-1.888 8.032 20.96 47.232 25.92 96.16 17.28 146.784h-51.104l-0.128-0.16zM310.784 287.904c14.592-1.632 27.52-3.712 40.544-4.48 14.24-0.864 19.872-5.856 19.584-20.288a664.96 664.96 0 0 0-1.92-41.472 154.944 154.944 0 0 0-20.8-66.496c-8.736-14.752-14.592-14.688-21.824 0.48-5.856 12.32-10.656 25.6-13.76 38.88A229.952 229.952 0 0 0 310.816 288v-0.064z m396.576-0.288c0-24.064 1.856-48.352-0.576-72.224-1.92-18.72-8.608-37.12-14.688-55.168-2.048-6.144-8.704-10.752-13.216-16.096-4.704 5.408-10.656 10.112-13.888 16.32-17.216 33.44-20.352 69.76-19.072 106.56 0.192 6.848 2.208 15.488 12.736 16.256 15.296 1.152 30.592 2.72 48.704 4.352z" fill="#808182" p-id="5669"></path><path d="M503.584 645.344c-22.432-0.288-48.832-1.6-74.08-14.72-51.584-27.104-66.368-90.88-30.112-136.32 34.464-43.2 98.688-60.768 154.4-42.528 34.176 11.2 61.856 30.752 75.872 64.384 19.04 45.632 1.792 89.28-39.968 112.448-28.8 15.936-38.816 16.736-86.176 16.736h0.064z m2.72-31.904c9.856 0 18.816 0.704 27.616-0.16 24.224-2.336 46.08-10.816 60.8-31.04 22.592-31.04 8.128-62.592-18.688-83.232-41.344-31.904-109.056-29.12-147.264 10.176-28.576 29.44-20.576 75.776 16.672 93.504 19.84 9.472 40.32 12.384 60.864 10.752zM378.624 480c-0.064 19.264-15.68 36.64-32.8 36.576-17.536-0.128-31.2-12.96-31.04-29.12 0.128-20.8 14.816-36.384 34.048-36.16 16.64 0.128 29.952 12.928 29.888 28.768l-0.064-0.096z m259.2 0c-0.128-16 12.96-28.576 29.824-28.8 18.944-0.192 34.048 15.808 34.112 36.16 0 16.192-13.792 29.152-31.104 29.216-17.152 0.064-32.672-17.248-32.832-36.608z" fill="#808182" p-id="5670"></path><path d="M508.224 580.256c-11.712 0.192-17.6-6.08-14.656-17.312 1.92-7.36 0.864-11.904-5.696-16.448-6.656-4.576-13.44-10.976-7.456-20.16 5.568-8.544 13.792-5.696 21.888-3.552a24.96 24.96 0 0 0 11.456-0.448c7.776-1.92 15.424-3.84 20.736 4.288 5.344 8.256-0.224 14.72-6.016 18.656-8.096 5.568-9.152 11.52-7.168 20.384 2.08 9.088-3.52 14.4-13.088 14.528v0.064z" fill="#808182" p-id="5671"></path></svg>
  );
}

// QianwenIcon
export const QianwenIcon: React.FC<{}> = ({}) => {
  return (
    <svg t="1748864453658" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6740" width="20" height="20"><path d="M1019.364785 620.816931L891.797142 397.807295 946.450846 293.15069a29.097778 29.097778 0 0 0 6.399732-36.393472l-70.184053-126.586684a30.078737 30.078737 0 0 0-24.574968-13.652427H597.4945L539.171949 14.549389a27.348852 27.348852 0 0 0-20.906122-14.549389H380.628607a29.139776 29.139776 0 0 0-24.616967 14.549389v5.545767L225.797108 243.062793H100.919352a29.182775 29.182775 0 0 0-25.513928 13.653427L3.428446 384.11187a32.766624 32.766624 0 0 0 0 29.182775L132.831012 638.096205 74.508461 740.064923a32.766624 32.766624 0 0 0 0 29.05478l66.514207 116.561105a29.905744 29.905744 0 0 0 25.513929 14.505391H427.132654l62.845361 109.222414A30.078737 30.078737 0 0 0 512.762058 1024H660.382859a29.139776 29.139776 0 0 0 24.574968-14.549389l128.463606-224.843558h114.76818a31.91366 31.91366 0 0 0 24.660965-15.444352l66.471208-117.414069a28.158818 28.158818 0 0 0 0-30.9747l0.042999 0.042999z m-161.273228 14.591387L791.57735 512.490479 518.265827 993.964261l-74.748861-122.87484h-273.268525l65.618244-119.205994h139.386147L101.856313 272.244568h143.055993L380.671605 30.121735l68.34913 119.247993-70.184053 122.87484H925.501726l-69.202094 121.936879 137.594222 241.183873H858.134555z" fill="#605BEC" p-id="6741"></path><path d="M499.962596 699.320634l174.371677-274.719464H324.694955z" fill="#605BEC" p-id="6742"></path></svg>
  );
}

// 导出所有图标
export const AfireAIIcons = {
  GptIcon,
  DeepseekIcon,
  ClaudeIcon,
  GeminiIcon,
  LlamaIcon,
  QianwenIcon
};

export default AfireAIIcons;