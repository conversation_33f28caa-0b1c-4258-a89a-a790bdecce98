.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.create-app-modal {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-header h2 {
    font-size: 18px;
    margin: 0;
    font-weight: 500;
}

.close-button {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.app-name-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 16px;
    background-color: #f5f7f9;
}

.app-description-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    min-height: 100px;
    resize: vertical;
    background-color: #f5f7f9;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
}

.cancel-button {
    padding: 6px 10px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: white;
    font-size: 12px;
    cursor: pointer;
}

.create-button {
    padding: 6px 10px;
    border: none;
    border-radius: 6px;
    background-color: #004aeb;
    color: white;
    font-size: 12px;
    cursor: pointer;
}

.create-button:hover {
    background-color: #155aef;
}


/* 新增的图标和名称容器样式 */

.icon-name-container {
    display: flex;
    align-items: center;
    gap: 16px;
}

.icon-upload-container {
    width: 48px;
    /* 必要ならここでサイズを統一 */
    height: 48px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-preview {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    background-color: #f3f2ef;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.2s ease;
    display: flex;
    /* ここでもセンタリングを二重に保険 */
    align-items: center;
    justify-content: center;
}


/* ③ 画像を入れた場合は “contain” で中央＆余白ありに */

.icon-preview img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    /* cover → contain に変更して中央寄せ */
}


/* ④ デフォルト絵文字の場合も完全中央寄せ */

.default-icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    line-height: 1;
    /* 絵文字の上下余白をなくす */
}

.icon-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.icon-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.app-name-inline {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    background-color: white;
    outline: none;
    transition: border-color 0.2s ease;
}

.app-name-inline:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}