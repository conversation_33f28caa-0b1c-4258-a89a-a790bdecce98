.addNodeContainer {
    position: relative;
    display: inline-block;
}

.addNodeButton {
    background-color: #ffffff;
    color: #0d6efd;
    border: 1px solid #dee2e6;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    margin-left: 8px;
    font-weight: 300;
    text-align: center;
    vertical-align: middle;
    user-select: none;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    width: 110px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.addNodeButton:hover {
    background-color: #f0f0f0;
    /* Light gray background on hover */
    color: #0056b3;
    /* Darker blue text on hover */
    border-color: #ced4da;
    /* Slightly darker gray border on hover */
}

.addNodeButton.open {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.addNodeContent {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.addNodeIcon {
    margin-bottom: 2px;
    font-size: 14px;
}

.menu {
    position: absolute;
    z-index: 1000;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    padding: 8px 30px 8px 8px;
    /* 增加右侧内边距，为关闭按钮留出空间 */
    min-width: 200px;
    max-height: 600px;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    transform: translateY(-100%);
    /* 将菜单向上移动自身高度的100% */
    margin-top: -10px;
    /* 添加一些间距 */
}

.addNode-menuItem {
    text-align: left;
    padding: 4px 2px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
    /* 增加图标与文本之间的间距 */
    margin: 2px 0;
    border: none;
    background: none;
    cursor: pointer;
    width: 100%;
}


/* 图标容器样式 */

.icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background-color: #f8f8f8;
    /* 更浅的灰色背景 */
    border-radius: 8px;
    /* 增加圆角 */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    /* 添加轻微阴影 */
    transition: all 0.2s ease;
    /* 添加过渡效果 */
}


/* 菜单项悬停时图标容器的样式 */

.addNode-menuItem:hover .icon-container {
    background-color: #d5e9fd;
    /* 悬停时背景色变深 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    /* 悬停时阴影增强 */
}

.addNode-menuItem:hover {
    background-color: #f5f5f5;
    /* 更浅的灰色背景 */
    color: #0056b3;
    /* 深蓝色文本 */
}

.closeButton {
    position: absolute;
    top: 8px;
    /* 调整为正值，位于菜单内部顶部 */
    right: 8px;
    /* 调整为正值，位于菜单内部右侧 */
    width: 24px;
    /* 减小按钮大小 */
    height: 24px;
    /* 减小按钮大小 */
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 50%;
    /* 圆形按钮 */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1001;
    /* 要比 .menu 高，确保在最前面 */
    transition: color 0.2s;
    font-size: 14px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.closeButton:hover {
    color: #ff0000;
    /* 红色图标 */
    background-color: #f0f0f0;
    /* 浅灰色背景 */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    /* 增强阴影效果 */
}