.button {
    background-color: #ffffff;
    /* White background */
    color: #0d6efd;
    /* Blue text, like "実行" */
    border: 1px solid #dee2e6;
    /* Light gray border */
    padding: 8px 16px;
    /* More spacious padding, like "実行" */
    border-radius: 8px;
    /* More rounded corners, as per image */
    cursor: pointer;
    margin-left: 4px;
    font-weight: 500;
    /* Medium font weight for emphasis */
    text-align: center;
    /* Ensure text is centered */
    vertical-align: middle;
    /* Align with other inline elements */
    user-select: none;
    /* Prevent text selection on click */
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    /* 确保文字横向显示 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    writing-mode: horizontal-tb;
    min-width: auto;
}

.button:hover {
    background-color: #f0f0f0;
    /* Light gray background on hover */
    color: #0056b3;
    /* Darker blue text on hover */
    border-color: #ced4da;
    /* Slightly darker gray border on hover */
}

.button-row {
    display: flex;
    gap: 2px;
    margin-bottom: 16px;
}

.icon-small {
    font-size: 0.8em;
    margin-right: 8px;
    display: inline-flex;
    align-items: center;
}


/* Loading动画样式 */

.loading-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}


/* 禁用状态的按钮样式 */

.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #dee2e6;
}

.button:disabled:hover {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #dee2e6;
}