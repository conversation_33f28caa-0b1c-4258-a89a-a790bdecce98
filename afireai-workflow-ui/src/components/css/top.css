.header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    height: 50px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.leftSection,
.centerSection,
.rightSection {
    display: flex;
    align-items: center;
    margin-left: 12px;
    margin-right: 8px;
}

.workspaceSelector {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 600;
    color: #333;
    margin-right: 12px;
}

.workspaceSelector span {
    margin-right: 4px;
}

.upgradeButton {
    background-color: #4a90e2;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
}

.upgradeButton:hover {
    background-color: #357abd;
}

.centerSection {
    gap: 8px;
}

.navButton {
    background-color: transparent;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #555;
    font-size: 14px;
    border-radius: 4px;
}

.navButton .icon {
    margin-right: 6px;
    font-size: 22px;
}

.navButton:hover {
    background-color: #f0f0f0;
}

.navButton.active {
    background-color: #e6f0fa;
    /* Light blue for active tab */
    color: #4a90e2;
    font-weight: 600;
}

.rightSection {
    gap: 16px;
}

.accountInfo {
    position: relative;
}

.accountButton {
    background-color: #4a90e2;
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
}

.avatar {
    /* Styles for the 'D' avatar */
}

.accountMenu {
    position: absolute;
    top: 40px;
    /* Adjust as needed */
    right: 0;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 240px;
    z-index: 1000;
    padding: 8px 0;
}

.userInfo {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 8px;
}

.avatarLarge {
    background-color: #4a90e2;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
    margin-right: 12px;
}

.email {
    font-size: 12px;
    color: #777;
}

.menuItem {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.menuItem:hover {
    background-color: #f0f0f0;
}

.menuIcon {
    margin-right: 10px;
    color: #555;
    font-size: 22px;
}

.divider {
    border: none;
    /* 移除默认边框 */
    height: 1px;
    /* 设置分割线的高度 */
    background-color: transparent;
    /* 确保背景透明 */
    margin: 8px 0;
    /* 上下边距 */
    border-top: 1px solid #e0e0e0;
    /* 使用浅灰色作为上边框颜色 */
}

.starCount {
    margin-left: auto;
    font-size: 12px;
    color: #777;
    background-color: #f0f0f0;
    padding: 2px 6px;
    border-radius: 10px;
}

.version {
    margin-left: auto;
    font-size: 12px;
    color: #777;
    background-color: #e6f7ea;
    /* Light green for version */
    color: #28a745;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: bold;
}