import React, { useCallback, useState } from 'react';
import { Panel, useReactFlow } from '@xyflow/react';
import { useParams } from 'react-router-dom';
import './css/header.css';
import { FaPlay, FaRegSave } from 'react-icons/fa';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';
import { updateApp } from '../services/appService';
import { showSuccess, showError, showInfo } from '../utils/toast';

interface HeaderProps {
  rfInstance: any;
  flowKey: string;
  setNodes: (nodes: any) => void;
  setEdges: (edges: any) => void;
  // setViewport is part of useReactFlow, so we can get it from there if needed directly in Header
  // getNodeId is also part of App, will pass it as prop
  getNodeId: () => string;
  onWorkflowRun?: () => Promise<void>;
  onSetIsRunning?: (setIsRunning: (value: boolean) => void) => void; // 新增：传递setIsRunning函数
}

const Header: React.FC<HeaderProps> = ({ rfInstance, flowKey, setNodes, setEdges, getNodeId, onWorkflowRun, onSetIsRunning }) => {
  const { setViewport } = useReactFlow();
  const { id: appId } = useParams<{ id: string }>();
  const [isSaving, setIsSaving] = useState(false);
  const [isRunning, setIsRunning] = useState(false);

  // 暴露关闭loading的方法给父组件
  React.useEffect(() => {
    if (onSetIsRunning) {
      onSetIsRunning(setIsRunning);
    }
  }, [onSetIsRunning, setIsRunning]);

  const onSave = useCallback(async () => {
    if (rfInstance && appId) {
      setIsSaving(true);
      try {
        // 获取当前工作流数据
        const flow = rfInstance.toObject();

        // 保存到本地存储（可选）
        localStorage.setItem(flowKey, JSON.stringify(flow));

        // 准备API请求数据
        const flowJson = JSON.stringify(flow);

        console.log(`Saving workflow for app_id: ${appId}`);
        console.log('Flow data structure:', Object.keys(flow));
        console.log('Viewport info:', flow.viewport);
        console.log('Flow data:', flowJson);

        // 调用更新API
        const response = await updateApp(appId, { flow_json: flowJson });

        console.log('Update response:', response);

        // 显示成功消息
        // showSuccess("ワークフローが正常に保存されました");
      } catch (error) {
        console.error("Failed to save workflow:", error);
        showError(`ワークフローの保存に失敗しました: ${error instanceof Error ? error.message : '未知のエラー'}`);
      } finally {
        setIsSaving(false);
      }
    } else {
      if (!rfInstance) {
        console.error("ReactFlow instance is not available");
        showError("ReactFlowインスタンスが利用できません");
      }
      if (!appId) {
        console.error("App ID is not available");
        showError("アプリIDが利用できません");
      }
    }
  }, [rfInstance, flowKey, appId]);

  const onRun = useCallback(async () => {
    if (!rfInstance || !appId) {
      if (!rfInstance) {
        console.error("ReactFlow instance is not available");
        showError("ReactFlowインスタンスが利用できません");
      }
      if (!appId) {
        console.error("App ID is not available");
        showError("アプリIDが利用できません");
      }
      return;
    }

    setIsRunning(true);
    try {
      console.log(`Saving and running workflow for app_id: ${appId}`);

      // 先保存工作流
      console.log("Step 1: Saving workflow...");
      const flow = rfInstance.toObject();

      // 保存到本地存储
      localStorage.setItem(flowKey, JSON.stringify(flow));

      // 准备API请求数据
      const flowJson = JSON.stringify(flow);

      // 调用更新API保存工作流
      await updateApp(appId, { flow_json: flowJson });
      console.log("Workflow saved successfully");

      // 然后运行工作流 - 使用新的UI侧控制器
      console.log("Step 2: Running workflow...");
      if (onWorkflowRun) {
        console.log('Starting workflow execution with UI controller');
        await onWorkflowRun();
      }

      console.log('Workflow run successfully');

      // 显示成功消息
      // showSuccess("ワークフローを保存して実行を開始しました");
    } catch (error) {
      console.error("Failed to save and run workflow:", error);
      showError(`ワークフローの保存・実行に失敗しました: ${error instanceof Error ? error.message : '未知のエラー'}`);
      // 只有出错时才立即关闭loading
      setIsRunning(false);
    }
    // 成功时不关闭loading，让handleWorkflowComplete来处理
  }, [rfInstance, flowKey, appId, onWorkflowRun]);

  return (
    <Panel position="top-right">
      <div className="button-row">
        <button className="button icon-small" onClick={onSave} disabled={isSaving}>
          {isSaving ? (
            <AiOutlineLoading3Quarters className="loading-icon" />
          ) : (
            <FaRegSave />
          )}
          {isSaving ? '保存中' : '保存する'}
        </button>
        <button className="button icon-small" onClick={onRun} disabled={isRunning}>
          {isRunning ? (
            <AiOutlineLoading3Quarters className="loading-icon" />
          ) : (
            <FaPlay />
          )}
          {isRunning ? '実行中' : '実行する'}
        </button>

      </div>
    </Panel>
  );
};

export default Header;