/**
 * React Hook for the new UI-side workflow controller
 * 完全替代API侧orchestrator的UI侧工作流控制
 */

import { useCallback, useRef, useState } from 'react';
import { Node, Edge } from '@xyflow/react';
import {
  UIWorkflowController,
  WorkflowControllerConfig,
  NodeExecutionStatus,
  NodeExecutionState
} from '../utils/UIWorkflowController';

export interface UseUIWorkflowControllerOptions {
  apiBaseUrl?: string;
  onNodeStateChange?: (nodeId: string, state: NodeExecutionState) => void;
  onWorkflowComplete?: (success: boolean, results: Map<string, NodeExecutionState>) => void;
}

export interface UIWorkflowControllerState {
  isExecuting: boolean;
  nodeStates: Map<string, NodeExecutionState>;
  error: string | null;
}

export const useUIWorkflowController = (options: UseUIWorkflowControllerOptions = {}) => {
  const controllerRef = useRef<UIWorkflowController | null>(null);
  const [state, setState] = useState<UIWorkflowControllerState>({
    isExecuting: false,
    nodeStates: new Map(),
    error: null
  });

  // 使用ref来保存最新的回调函数，避免重新创建控制器
  const callbacksRef = useRef(options);
  callbacksRef.current = options;

  // 初始化控制器
  const initializeController = useCallback(() => {
    if (!controllerRef.current) {
      const config: WorkflowControllerConfig = {
        apiBaseUrl: options.apiBaseUrl || 'http://localhost:8000',
        onNodeStateChange: (nodeId, nodeState) => {
          setState(prev => {
            const newNodeStates = new Map(prev.nodeStates);
            newNodeStates.set(nodeId, nodeState);

            return {
              ...prev,
              nodeStates: newNodeStates
            };
          });

          // 调用外部回调 - 使用ref获取最新的回调
          callbacksRef.current.onNodeStateChange?.(nodeId, nodeState);
        },
        onWorkflowComplete: (success, results) => {
          setState(prev => ({
            ...prev,
            isExecuting: false,
            nodeStates: new Map(results)
          }));

          // 调用外部回调 - 使用ref获取最新的回调
          callbacksRef.current.onWorkflowComplete?.(success, results);
        }
      };

      controllerRef.current = new UIWorkflowController(config);
    }

    return controllerRef.current;
  }, [options.apiBaseUrl]); // 只依赖apiBaseUrl，不依赖回调函数

  // 执行工作流
  const executeWorkflow = useCallback(async (nodes: Node[], edges: Edge[], app_id: string, flow_json: string) => {
    try {
      setState(prev => ({ ...prev, isExecuting: true, error: null }));

      const controller = initializeController();
      await controller.executeWorkflow(nodes, edges, app_id, flow_json);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Workflow execution failed:', error);
      setState(prev => ({
        ...prev,
        isExecuting: false,
        error: errorMessage
      }));
    }
  }, [initializeController]);

  // 停止执行
  const stopExecution = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.stopExecution();
      setState(prev => ({ ...prev, isExecuting: false }));
    }
  }, []);

  // 获取节点状态
  const getNodeState = useCallback((nodeId: string): NodeExecutionState | undefined => {
    return state.nodeStates.get(nodeId);
  }, [state.nodeStates]);

  // 获取节点状态（简化版本）
  const getNodeStatus = useCallback((nodeId: string): NodeExecutionStatus => {
    const nodeState = state.nodeStates.get(nodeId);
    return nodeState?.status || 'PENDING';
  }, [state.nodeStates]);

  // 重置状态
  const resetState = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.resetState();
    }
    setState({
      isExecuting: false,
      nodeStates: new Map(),
      error: null
    });
  }, []);

  // 检查是否有节点正在运行
  const hasRunningNodes = useCallback((): boolean => {
    return Array.from(state.nodeStates.values()).some(
      nodeState => nodeState.status === 'RUNNING'
    );
  }, [state.nodeStates]);

  // 检查是否有失败的节点
  const hasFailedNodes = useCallback((): boolean => {
    return Array.from(state.nodeStates.values()).some(
      nodeState => nodeState.status === 'FAILED'
    );
  }, [state.nodeStates]);

  // 获取执行统计
  const getExecutionStats = useCallback(() => {
    const states = Array.from(state.nodeStates.values());
    return {
      total: states.length,
      pending: states.filter(s => s.status === 'PENDING').length,
      running: states.filter(s => s.status === 'RUNNING').length,
      success: states.filter(s => s.status === 'SUCCESS').length,
      failed: states.filter(s => s.status === 'FAILED').length,
      waitingManual: states.filter(s => s.status === 'WAITING_MANUAL').length
    };
  }, [state.nodeStates]);

  // 执行单个节点
  const executeSingleNode = useCallback(async (nodeId: string, nodes: Node[], edges: Edge[], app_id: string, flow_json: string) => {
    try {
      const controller = initializeController();
      await controller.executeSingleNode(nodeId, nodes, edges, app_id, flow_json);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Single node execution failed:', error);
      setState(prev => ({
        ...prev,
        error: errorMessage
      }));
      throw error;
    }
  }, [initializeController]);

  // 检查节点是否可以执行
  const canExecuteSingleNode = useCallback((nodeId: string, nodes: Node[], edges: Edge[]) => {
    const controller = initializeController();
    return controller.canExecuteSingleNode(nodeId, nodes, edges);
  }, [initializeController]);

  return {
    // 状态
    isExecuting: state.isExecuting,
    nodeStates: state.nodeStates,
    error: state.error,

    // 方法
    executeWorkflow,
    executeSingleNode,
    stopExecution,
    getNodeState,
    getNodeStatus,
    resetState,

    // 辅助方法
    hasRunningNodes,
    hasFailedNodes,
    getExecutionStats,
    canExecuteSingleNode,

    // 控制器实例（用于高级操作）
    controller: controllerRef.current
  };
};
