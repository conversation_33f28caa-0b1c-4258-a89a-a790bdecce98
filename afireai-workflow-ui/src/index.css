/* 全局样式重置 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: var(--font-family);
}

:root {
    /* 颜色变量 */
    --primary-color: #4a90e2;
    --secondary-color: #5e7cff;
    --text-color: #333333;
    --light-text-color: #666666;
    --background-color: #ffffff;
    --light-background: #f5f6fa;
    --border-color: #e0e0e0;
    --hover-color: #f0f0f0;
    --active-color: #eef2ff;
    --error-color: #ff4d4f;
    --success-color: #52c41a;
    --warning-color: #faad14;
    /* 字体变量 */
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-size-base: 14px;
    --font-size-small: 12px;
    --font-size-large: 16px;
    --font-size-xlarge: 18px;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 600;
    /* 基础设置 */
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-color);
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

html,
body,
#root {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

body {
    /* background-color: var(--light-background); */
}

#root {
    display: flex;
    flex-direction: column;
}


/* 链接样式 */

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--secondary-color);
}


/* 按钮基础样式 */

button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: inherit;
    font-weight: var(--font-weight-medium);
}

button:focus {
    outline: none;
}


/* 输入框基础样式 */

input,
textarea,
select {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    outline: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    font-weight: var(--font-weight-normal);
}

input:focus,
textarea:focus,
select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}


/* 工作流编辑器特定样式 */

.react-flow {
    background-color: var(--light-background);
}

.react-flow__node {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.react-flow__handle {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 2px solid white;
}


/* 标题和文本样式 */

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--font-family);
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    margin-bottom: 0.5em;
}

h1 {
    font-size: var(--font-size-xlarge);
}

h2 {
    font-size: var(--font-size-large);
}

h3,
h4,
h5,
h6 {
    font-size: var(--font-size-base);
}

p {
    font-size: var(--font-size-base);
    margin-bottom: 1em;
}

.text-small {
    font-size: var(--font-size-small);
    color: var(--light-text-color);
}

.text-bold {
    font-weight: var(--font-weight-bold);
}

.text-medium {
    font-weight: var(--font-weight-medium);
}


/* 表单标签样式 */

label {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin-bottom: 0.5em;
    display: inline-block;
}


/* 模态框标题样式 */

.modal-header h2 {
    font-size: var(--font-size-large);
    font-weight: var(--font-weight-bold);
}


/* 响应式设计基础 */

@media (max-width: 768px) {
     :root {
        --font-size-base: 13px;
        --font-size-small: 11px;
        --font-size-large: 15px;
        --font-size-xlarge: 17px;
    }
}