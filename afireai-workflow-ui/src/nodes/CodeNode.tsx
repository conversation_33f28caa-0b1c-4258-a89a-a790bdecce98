import { memo } from 'react';
import { Position, Handle, useReactFlow, type NodeProps, type Node } from '@xyflow/react';
import { FaCode } from 'react-icons/fa';
import './css/CodeNode.css';

// 导入全局事件总线
import { useNodeEvents } from '../utils/NodeEventContext';
// 导入状态指示器
import NodeStatusIndicator from '../components/NodeStatusIndicator';
// 导入执行按钮
import NodeExecuteButton from '../components/NodeExecuteButton';
// 导入节点执行上下文
import { useNodeExecution } from '../utils/NodeExecutionContext';

interface CodeNodeData extends Record<string, unknown> {
  label?: string;
  language?: string;
  status?: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED';
}

function CodeNode({ id, data }: NodeProps<Node<CodeNodeData>>) {
  const { updateNodeData } = useReactFlow();
  const { selectNode } = useNodeEvents();

  const handleNodeClick = () => {
    // 触发节点选择事件，传递当前节点信息
    selectNode({ id, data, type: 'codeNode' });
  };

  const { executeSingleNode } = useNodeExecution();

  const handleExecuteNode = async (nodeId: string) => {
    console.log('Execute Code Node:', nodeId);
    try {
      await executeSingleNode(nodeId);
    } catch (error) {
      console.error('Failed to execute Code node:', error);
    }
  };

  return (
    <div className="code-node" onClick={handleNodeClick}>
      <NodeStatusIndicator status={data.status} />
      <NodeExecuteButton nodeId={id} onExecute={handleExecuteNode} />
      <div className="code-node-header">
        <div className="code-node-icon">
          <FaCode />
        </div>
        <div className="code-node-title">
          {data.label || 'コード実行'}
        </div>
      </div>

      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />

      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />
    </div>
  );
}

export default memo(CodeNode);
