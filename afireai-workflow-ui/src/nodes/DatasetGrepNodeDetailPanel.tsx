import React, { useState } from 'react';
import { FaTimes, FaBook, FaEllipsisH, FaQuestionCircle, FaPlus, FaFolder, FaPen, FaTrash, FaChevronDown, FaChevronRight } from 'react-icons/fa';
import { IoBookOutline } from 'react-icons/io5';
import { BiSlider } from 'react-icons/bi';
import { useReactFlow } from '@xyflow/react';
import './css/NodeDetailPanel.css';
import './css/DatasetGrepNodeDetailPanel.css';

interface DatasetGrepNodeDetailPanelProps {
  node: any;
  onClose: () => void;
}

const DatasetGrepNodeDetailPanel: React.FC<DatasetGrepNodeDetailPanelProps> = ({ node, onClose }) => {
  const { setNodes } = useReactFlow();
  const [description, setDescription] = useState<string>('');
  const [showKnowledgeModal, setShowKnowledgeModal] = useState<boolean>(false);
  const [selectedDataset, setSelectedDataset] = useState<string>(node.data.dataset || '社内業務まとめ資料.pdf...');
  const [metadataFilter, setMetadataFilter] = useState<string>('無効');
  const [outputExpanded, setOutputExpanded] = useState<boolean>(false);

  // 更新节点数据
  const updateNodeData = (data: any) => {
    setNodes((nodes) =>
      nodes.map((n) => {
        if (n.id === node.id) {
          return {
            ...n,
            data: {
              ...n.data,
              ...data,
            },
          };
        }
        return n;
      })
    );
  };

  // 打开知识选择模态框
  const handleOpenKnowledgeModal = () => {
    setShowKnowledgeModal(true);
  };

  // 关闭知识选择模态框
  const handleCloseKnowledgeModal = () => {
    setShowKnowledgeModal(false);
  };

  // 添加选择的知识
  const handleAddKnowledge = () => {
    updateNodeData({ dataset: selectedDataset });
    setShowKnowledgeModal(false);
  };

  return (
    <div className="node-detail-panel">
      <div className="node-detail-header">
        <div className="node-detail-title">
          <div className="dataset-grep-node-icon dataset-grep-node-icon-color">
            <IoBookOutline />
          </div>
          <h2>{node.data.label || '知識検索'}</h2>
        </div>
        <div className="node-detail-actions">
          <button className="icon-button" onClick={onClose}>
            <FaTimes />
          </button>
        </div>
      </div>

      <div className="node-detail-content">
        <div className="description-section">
          <textarea
            placeholder="説明を追加..."
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="description-input"
          />
        </div>

        <div className="search-variable-section">
          <div className="section-header">
            <h3>検索変数 <span className="required-mark">*</span></h3>
          </div>

          <div className="variable-selector">
            <div className="variable-placeholder">
              <span className="variable-icon">{'{x}'}</span> 変数値を設定
            </div>
          </div>
        </div>

        <div className="knowledge-base-section">
          <div className="section-header">
            <h3>ナレッジベース <span className="required-mark">*</span></h3>
            <div className="knowledge-actions">
              <button className="search-settings-button">
                <BiSlider /> 検索設定
              </button>
              <button className="add-knowledge-button" onClick={handleOpenKnowledgeModal}>
                <FaPlus />
              </button>
            </div>
          </div>

          {selectedDataset && (
            <div className="selected-knowledge">
              <div className="knowledge-icon">
                <FaFolder />
              </div>
              <div className="knowledge-name">
                {selectedDataset}
              </div>
              <div className="knowledge-actions">
                <button className="edit-button">
                  <FaPen />
                </button>
                <button className="delete-button">
                  <FaTrash />
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="output-section">
          <div className="section-header" onClick={() => setOutputExpanded(!outputExpanded)}>
            <h3>出力変数 <span className="optional-mark">{outputExpanded ? '▾' : '▸'}</span></h3>
          </div>

          {outputExpanded && (
            <div className="output-variables">
              <div className="output-variable">
                <div className="variable-name">result <span className="variable-type">Array[Object]</span></div>
                <div className="variable-description">検索結果セグメント</div>

                <div className="variable-properties">
                  <div className="property">
                    <div className="property-name">content <span className="property-type">string</span></div>
                    <div className="property-description">セグメント内容</div>
                  </div>

                  <div className="property">
                    <div className="property-name">title <span className="property-type">string</span></div>
                    <div className="property-description">セグメントタイトル</div>
                  </div>

                  <div className="property">
                    <div className="property-name">url <span className="property-type">string</span></div>
                    <div className="property-description">セグメントURL</div>
                  </div>

                  <div className="property">
                    <div className="property-name">icon <span className="property-type">string</span></div>
                    <div className="property-description">セグメントアイコン</div>
                  </div>

                  <div className="property">
                    <div className="property-name">metadata <span className="property-type">object</span></div>
                    <div className="property-description">メタデータ</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

      </div>

      {/* 知识选择模态框 */}
      {showKnowledgeModal && (
        <div className="knowledge-modal-overlay">
          <div className="knowledge-modal">
            <h2>参照する知識を選択</h2>

            <div className="knowledge-item">
              <div className="knowledge-item-icon">
                <FaFolder />
              </div>
              <div className="knowledge-item-name">
              社内業務まとめ資料
              </div>
              <div className="knowledge-item-tag">
                高品質・ハイブリッド検索
              </div>
            </div>

            <div className="knowledge-count">
              1 選択された知識
            </div>

            <div className="knowledge-modal-actions">
              <button className="cancel-button" onClick={handleCloseKnowledgeModal}>
                キャンセル
              </button>
              <button className="add-button" onClick={handleAddKnowledge}>
                追加
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DatasetGrepNodeDetailPanel;
