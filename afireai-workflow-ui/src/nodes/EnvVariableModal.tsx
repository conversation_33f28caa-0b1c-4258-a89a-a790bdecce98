import React, { useState } from 'react';
import { FaTimes, FaQuestionCircle } from 'react-icons/fa';

interface EnvVariableModalProps {
  onClose: () => void;
  onSave: (variable: { name: string; type: string; value: string }) => void;
}

const EnvVariableModal: React.FC<EnvVariableModalProps> = ({ onClose, onSave }) => {
  const [variableName, setVariableName] = useState('');
  const [variableValue, setVariableValue] = useState('');
  const [selectedType, setSelectedType] = useState('String');

  const handleSave = () => {
    if (variableName.trim() === '') {
      alert('変数名を入力してください');
      return;
    }
    
    onSave({
      name: variableName,
      type: selectedType,
      value: variableValue
    });
    
    onClose();
  };

  return (
    <div className="env-variable-modal" onClick={onClose}>
      <div className="env-variable-content" onClick={(e) => e.stopPropagation()}>
        <div className="env-variable-header">
          <h2>環境変数を追加</h2>
          <button className="env-variable-close" onClick={onClose}>
            <FaTimes />
          </button>
        </div>
        
        <div className="env-variable-form">
          <div className="env-variable-field">
            <label>タイプ</label>
            <div className="env-variable-types">
              <div 
                className={`type-option ${selectedType === 'String' ? 'selected' : ''}`}
                onClick={() => setSelectedType('String')}
              >
                String
              </div>
              <div 
                className={`type-option ${selectedType === 'Number' ? 'selected' : ''}`}
                onClick={() => setSelectedType('Number')}
              >
                Number
              </div>
              <div 
                className={`type-option ${selectedType === 'Secret' ? 'selected' : ''}`}
                onClick={() => setSelectedType('Secret')}
              >
                Secret <FaQuestionCircle style={{ fontSize: '14px', marginLeft: '4px' }} />
              </div>
            </div>
          </div>
          
          <div className="env-variable-field">
            <label>変数名</label>
            <input
              type="text"
              className="env-variable-input"
              placeholder="変数名を入力"
              value={variableName}
              onChange={(e) => setVariableName(e.target.value)}
            />
          </div>
          
          <div className="env-variable-field">
            <label>値</label>
            <input
              type={selectedType === 'Secret' ? 'password' : 'text'}
              className="env-variable-input"
              placeholder="変数値を入力"
              value={variableValue}
              onChange={(e) => setVariableValue(e.target.value)}
            />
          </div>
          
          <div className="env-variable-actions">
            <button className="env-variable-button env-variable-cancel" onClick={onClose}>
              キャンセル
            </button>
            <button className="env-variable-button env-variable-save" onClick={handleSave}>
              保存
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnvVariableModal;
