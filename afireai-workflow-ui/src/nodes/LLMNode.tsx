import { memo } from 'react';
import { Posi<PERSON>, Handle, useReactFlow, type NodeProps, type Node } from '@xyflow/react';
import { FaRobot } from 'react-icons/fa';
import './css/LLMNode.css';

// 导入全局事件总线
import { useNodeEvents } from '../utils/NodeEventContext';
// 导入状态指示器
import NodeStatusIndicator from '../components/NodeStatusIndicator';
// 导入执行按钮
import NodeExecuteButton from '../components/NodeExecuteButton';
// 导入节点执行上下文
import { useNodeExecution } from '../utils/NodeExecutionContext';

interface LLMNodeData extends Record<string, unknown> {
  label?: string;
  model?: string;
  status?: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED';
}

function LLMNode({ id, data }: NodeProps<Node<LLMNodeData>>) {
  const { updateNodeData } = useReactFlow();
  const { selectNode } = useNodeEvents();
  const { executeSingleNode } = useNodeExecution();

  const handleNodeClick = () => {
    // 触发节点选择事件，传递当前节点信息
    selectNode({ id, data, type: 'llmNode' });
  };

  const handleExecuteNode = async (nodeId: string) => {
    console.log('Execute LLM Node:', nodeId);
    try {
      await executeSingleNode(nodeId);
    } catch (error) {
      console.error('Failed to execute LLM node:', error);
    }
  };

  return (
    <div className="llm-node" onClick={handleNodeClick}>
      <NodeStatusIndicator status={data.status} />
      <NodeExecuteButton nodeId={id} onExecute={handleExecuteNode} />
      <div className="llm-node-header">
        <div className="llm-node-icon">
          <FaRobot />
        </div>
        <div className="llm-node-title">
          {data.label || 'LLM'}
        </div>
      </div>

      {data.model && (
        <div className="llm-node-model">
          <div className="llm-model-icon">
            <img src="/openai-logo.svg" alt="Model" width="16" height="16" />
          </div>
          <div className="llm-model-name">
            {data.model}
          </div>
          <div className="llm-model-tag">
            CHAT
          </div>
        </div>
      )}

      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />

      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />

      <Handle
        type="source"
        position={Position.Bottom}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          // top: '50%',
          border: '1px solid white'
        }}
      />

    </div>
  );
}

export default memo(LLMNode);
