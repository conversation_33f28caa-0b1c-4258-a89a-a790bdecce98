import { memo } from 'react';
import { Position, Handle, useReactFlow, type NodeProps, type Node } from '@xyflow/react';
import { GrPlay } from 'react-icons/gr';
import './css/StartNode.css';

// 导入全局事件总线
import { useNodeEvents } from '../utils/NodeEventContext';
// 导入状态指示器
import NodeStatusIndicator from '../components/NodeStatusIndicator';

interface StartNodeData extends Record<string, unknown> {
  label?: string;
  status?: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED';
}

function StartNode({ id, data }: NodeProps<Node<StartNodeData>>) {
  const { updateNodeData } = useReactFlow();
  const { selectNode } = useNodeEvents();

  const handleNodeClick = () => {
    // 触发节点选择事件，传递当前节点信息
    selectNode({ id, data, type: 'startNode' });
  };

  return (
    <div className="start-node" onClick={handleNodeClick}>
      <NodeStatusIndicator status={data.status} />
      <div className="start-node-header">
        <div className="start-node-icon">
          <GrPlay />
        </div>
        <div className="start-node-title">
          {data.label || '開始'}
        </div>
      </div>
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />
    </div>
  );
}

export default memo(StartNode);
