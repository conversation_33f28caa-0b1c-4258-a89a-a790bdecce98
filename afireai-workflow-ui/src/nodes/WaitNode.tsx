import { memo } from 'react';
import { Position, Handle, useReactFlow, type NodeProps, type Node } from '@xyflow/react';
import { FaClock } from 'react-icons/fa';
import './css/WaitNode.css';

// 导入全局事件总线
import { useNodeEvents } from '../utils/NodeEventContext';
// 导入状态指示器
import NodeStatusIndicator from '../components/NodeStatusIndicator';
// 导入执行按钮
import NodeExecuteButton from '../components/NodeExecuteButton';
// 导入节点执行上下文
import { useNodeExecution } from '../utils/NodeExecutionContext';

interface WaitNodeData extends Record<string, unknown> {
  label?: string;
  status?: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED';
}

function WaitNode({ id, data }: NodeProps<Node<WaitNodeData>>) {
  const { updateNodeData } = useReactFlow();
  const { selectNode } = useNodeEvents();
  const { executeSingleNode } = useNodeExecution();

  const handleNodeClick = () => {
    // 触发节点选择事件，传递当前节点信息
    selectNode({ id, data, type: 'waitNode' });
  };

  const handleExecuteNode = async (nodeId: string) => {
    console.log('Execute LLM Node:', nodeId);
    try {
      await executeSingleNode(nodeId);
    } catch (error) {
      console.error('Failed to execute LLM node:', error);
    }
  };

  return (
    <div className="wait-node" onClick={handleNodeClick}>
      <NodeStatusIndicator status={data.status} />
      <NodeExecuteButton nodeId={id} onExecute={handleExecuteNode} />
      <div className="wait-node-header">
        <div className="wait-node-icon">
          <FaClock />
        </div>
        <div className="wait-node-title">
          {data.label || '承認待ち'}
        </div>
      </div>
      
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#f39c12',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />
    </div>
  );
}

export default memo(WaitNode);