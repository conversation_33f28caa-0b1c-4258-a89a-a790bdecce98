.code-node {
    border: 1px solid #e0e0e0;
    padding: 8px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    width: 200px;
    transition: box-shadow 0.3s ease;
}

.code-node:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    background-color: #f4f6f6;
}

.code-node-header {
    display: flex;
    align-items: center;
}

.code-node-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background-color: #3b82f6;
    border-radius: 6px;
    margin-right: 6px;
}

.code-node-icon svg {
    width: 16px;
    height: 16px;
    color: white;
}

.code-node-title {
    font-weight: 500;
    font-size: 14px;
    color: #333;
}

.code-node-icon-small {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background-color: #3b82f6;
    border-radius: 4px;
    margin-right: 6px;
}

.code-node-icon-small svg {
    width: 12px;
    height: 12px;
    color: white;
}

.code-node-title-small {
    font-weight: 300;
    font-size: 12px;
    color: #333;
}