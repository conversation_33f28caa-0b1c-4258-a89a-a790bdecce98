.code-node-icon-color {
    background-color: #3b82f6;
}

.input-variables-section,
.code-editor-section,
.output-variables-section,
.retry-section,
.exception-section {
    margin-bottom: 8px;
    padding-bottom: 8px;
}

.input-variables-section {
    margin-bottom: 6px;
    padding-bottom: 6px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    cursor: pointer;
}

.section-header h3 {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin: 0;
}

.required-mark {
    color: #f56565;
}

.add-variable-button,
.add-output-variable-button {
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: #4a6cf7;
    font-size: 14px;
    cursor: pointer;
    width: 24px;
    height: 24px;
}

.add-output-variable-button {
    width: 100%;
    margin-top: 10px;
    height: 32px;
    border: 1px dashed #cbd5e0;
    border-radius: 6px;
    color: #718096;
}

.add-output-variable-button svg {
    margin-right: 5px;
}

.input-variables-list,
.output-variables-list {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.input-variable-item,
.output-variable-item {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 2px;
}

.variable-name-field {
    width: 120px;
    background-color: #f5f7f9;
    border-radius: 6px;
    overflow: hidden;
}

.variable-name-field input {
    width: 100%;
    border: none;
    background: transparent;
    padding: 8px 10px;
    color: #2d3748;
}

.variable-value-field {
    flex: 1;
    background-color: #f5f7f9;
    border-radius: 6px;
    padding: 6px 10px;
}

.variable-placeholder {
    color: #718096;
    display: flex;
    align-items: center;
    font-size: 13px;
}

.variable-icon {
    margin-right: 8px;
    color: #4a6cf7;
}

.variable-type-field {
    flex: 1;
    background-color: #f5f7f9;
    border-radius: 6px;
    padding: 5px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    position: relative;
    font-size: 13px;
    color: #2d3748;
    border: none;
    height: 30px;
}

.type-dropdown-icon {
    color: #718096;
    font-size: 12px;
}

.remove-variable-button {
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.remove-variable-button:hover {
    opacity: 1;
}

.code-editor-section {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
}

.code-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.language-selector {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    font-weight: 500;
    color: #2d3748;
}

.language-dropdown-icon {
    font-size: 12px;
    color: #718096;
}

.code-editor-actions {
    display: flex;
    gap: 5px;
}

.code-action-button {
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.code-editor {
    display: flex;
    position: relative;
    min-height: 150px;
}

.line-numbers {
    padding: 10px 8px;
    background-color: #f8fafc;
    border-right: 1px solid #e2e8f0;
    color: #a0aec0;
    font-family: monospace;
    font-size: 13px;
    text-align: right;
    user-select: none;
}

.code-textarea {
    flex: 1;
    border: none;
    padding: 10px;
    font-family: monospace;
    font-size: 13px;
    line-height: 1.5;
    resize: none;
    background-color: #fff;
    color: #2d3748;
}

.type-dropdown {
    position: absolute;
    top: calc(100% + 5px);
    right: 0;
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
    z-index: 10;
    width: 180px;
    max-height: 250px;
    overflow-y: auto;
    padding: 4px 0;
}

.type-option {
    padding: 10px 16px;
    font-size: 13px;
    color: #2d3748;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.15s ease;
    border: none;
    /* 移除边框 */
    outline: none;
    /* 移除点击时的外边框 */
}

.type-option:focus {
    outline: none;
    box-shadow: none;
}

.type-option:hover {
    background-color: #f7f7f7;
}

.type-option.selected {
    background-color: #ebf4ff;
    color: #4a6cf7;
}

.selected-icon {
    color: #4a6cf7;
    font-size: 12px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e2e8f0;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked+.toggle-slider {
    background-color: #4a6cf7;
}

input:checked+.toggle-slider:before {
    transform: translateX(20px);
}

.exception-dropdown {
    background-color: #f5f7f9;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 13px;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

.dropdown-icon {
    font-size: 12px;
    color: #718096;
}

.exception-description {
    font-size: 13px;
    color: #718096;
    margin-bottom: 15px;
}

.details-link {
    color: #4a6cf7;
    cursor: pointer;
}

.exception-result {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    font-size: 13px;
    color: #2d3748;
}

.result-type {
    font-size: 12px;
    color: #718096;
    margin-left: 5px;
}

.result-actions {
    display: flex;
    gap: 5px;
}

.result-action-button {
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.result-code {
    display: flex;
    min-height: 100px;
}

.result-content {
    flex: 1;
    margin: 0;
    padding: 10px;
    font-family: monospace;
    font-size: 13px;
    line-height: 1.5;
    white-space: pre-wrap;
    color: #2d3748;
}

.help-icon {
    font-size: 14px;
    color: #a0aec0;
    margin-left: 5px;
    cursor: pointer;
}