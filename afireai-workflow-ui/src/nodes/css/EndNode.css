.end-node {
    border: 1px solid #e0e0e0;
    padding: 8px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    width: 200px;
    transition: box-shadow 0.3s ease;
}

.end-node:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    background-color: #f4f6f6;
}

.end-node-header {
    display: flex;
    align-items: center;
    /* margin-bottom: 8px; */
    /* padding-bottom: 8px; */
    /* border-bottom: 1px solid #f0f0f0; */
}

.end-node-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background-color: #fde7e7;
    border-radius: 6px;
    margin-right: 6px;
}

.end-node-icon svg {
    width: 16px;
    height: 16px;
    color: #e74c3c;
}

.end-node-title {
    font-weight: 500;
    font-size: 14px;
    color: #333;
}

.end-node-icon-small {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background-color: #fde7e7;
    border-radius: 4px;
    margin-right: 6px;
}

.end-node-icon-small svg {
    width: 12px;
    height: 12px;
    color: #e74c3c;
}

.end-node-title-small {
    font-weight: 300;
    font-size: 12px;
    color: #333;
}