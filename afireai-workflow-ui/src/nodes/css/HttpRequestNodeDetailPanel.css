.http-request-node-detail-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-left: 1px solid #e0e0e0;
  overflow-y: auto;
}

.detail-panel-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f9f9f9;
}

.node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background-color: #8b5cf6;
  border-radius: 6px;
  margin-right: 10px;
}

.node-icon svg {
  width: 16px;
  height: 16px;
  color: white;
}

.detail-panel-header h2 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  flex-grow: 1;
}

.header-actions {
  display: flex;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
}

.detail-panel-content {
  padding: 16px;
  flex-grow: 1;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-group.required label::after {
  content: " *";
  color: #e53e3e;
}

.description-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.api-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.method-select {
  width: 100px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.url-input {
  flex-grow: 1;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.auth-button-group {
  display: flex;
  gap: 8px;
}

.auth-button, .import-button {
  padding: 6px 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
  font-size: 13px;
  cursor: pointer;
}

.form-section {
  margin-bottom: 20px;
}

.form-section h3 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.key-value-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.key-value-row {
  display: flex;
  gap: 8px;
}

.key-input, .value-input {
  flex-grow: 1;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.remove-button {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  color: #4a90e2;
  cursor: pointer;
  font-size: 14px;
  padding: 4px 0;
}

.body-type-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 12px;
}

.body-type-option {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  font-size: 14px;
}

.variable-setting-box {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.variable-setting-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  color: #4a90e2;
  cursor: pointer;
  font-size: 14px;
}

.detail-section {
  margin-bottom: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: #f9f9f9;
  cursor: pointer;
}

.section-header h3 {
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

.section-content {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
}

.timeout-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.output-variables {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.output-variable {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.variable-name {
  font-weight: 500;
  font-size: 14px;
}

.variable-type {
  font-size: 12px;
  color: #666;
}

.variable-description {
  font-size: 13px;
}

.exception-help-text {
  font-size: 13px;
  color: #666;
  margin-bottom: 12px;
}

.output-field {
  margin-bottom: 16px;
}

.output-field label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
}

.field-type {
  font-size: 12px;
  color: #666;
}

.body-textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  min-height: 80px;
  resize: vertical;
}

.status-code-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.json-editor {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
  padding: 8px;
  background-color: #f9f9f9;
}

.json-line {
  display: flex;
  gap: 8px;
}

.line-number {
  color: #999;
  user-select: none;
}

.editor-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 4px;
}

.editor-action-button {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
}

/* 认证模态框样式 */
.auth-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.auth-modal {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.auth-modal h3 {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 16px;
}

.auth-form-group {
  margin-bottom: 16px;
}

.auth-form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
}

.auth-type-buttons {
  display: flex;
  gap: 8px;
}

.auth-type-button {
  padding: 6px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
  font-size: 14px;
  cursor: pointer;
}

.auth-type-button.active {
  background-color: #4a90e2;
  color: white;
  border-color: #4a90e2;
}

.auth-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.auth-modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 20px;
}

.cancel-button {
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  cursor: pointer;
}

.save-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #1a56db;
  color: white;
  font-size: 14px;
  cursor: pointer;
}
