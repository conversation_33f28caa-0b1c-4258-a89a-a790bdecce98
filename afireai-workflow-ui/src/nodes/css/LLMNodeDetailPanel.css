.llm-node-icon-color {
  background-color: #5468ff !important;
  color: white !important;
}

.required-mark {
  color: #e74c3c;
  font-size: 14px;
}

.optional-mark {
  color: #999;
  font-size: 14px;
}

.help-icon {
  color: #999;
  font-size: 14px;
  margin-left: 4px;
  cursor: pointer;
}

.llm-model-section,
.llm-tools-section,
.context-section,
.system-prompt-section,
.vision-section,
.output-section,
.retry-section {
  margin-bottom: 16px;
}

.model-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 8px;
}

.selected-model {
  display: flex;
  align-items: center;
}

.model-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #10a37f;
  border-radius: 4px;
  margin-right: 8px;
}

.model-name {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.model-tag {
  font-size: 11px;
  padding: 2px 6px;
  background-color: #e0e0e0;
  border-radius: 4px;
  color: #666;
  margin-left: 8px;
}

.model-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

.model-dropdown {
  position: absolute;
  width: calc(100% - 24px);
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.model-option {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.model-option:hover {
  background-color: #f5f5f5;
}

.model-option.selected {
  background-color: #f0f7ff;
}

/* ツール選択機能のスタイル */
.llm-tools-section {
  position: relative;
}

.tools-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #fff;
  cursor: pointer;
  transition: border-color 0.2s ease;
  position: relative;
}

.tools-selector:hover {
  border-color: #5468ff;
}

.selected-tools {
  display: flex;
  align-items: center;
  flex: 1;
}

.tools-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: #5468ff;
  border-radius: 4px;
  margin-right: 8px;
}

.tools-icon svg {
  width: 12px;
  height: 12px;
  color: white;
}

.tools-count {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.tools-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

.tools-actions svg {
  width: 14px;
  height: 14px;
}

.tools-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 4px;
}

.tool-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.tool-option:hover {
  background-color: #f8f9fa;
}

.tool-option.selected {
  background-color: #e8f0ff;
}

.tool-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border: 1px solid #ccc;
  border-radius: 3px;
  margin-right: 8px;
  background-color: #fff;
}

.tool-option.selected .tool-checkbox {
  background-color: #5468ff;
  border-color: #5468ff;
  color: white;
}

.tool-checkbox svg {
  width: 10px;
  height: 10px;
}

.tool-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  background-color: #5468ff;
  border-radius: 3px;
  margin-right: 8px;
}

.tool-icon svg {
  width: 12px;
  height: 12px;
  color: white;
}

.tool-name {
  flex: 1;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.tool-tag {
  font-size: 11px;
  padding: 2px 6px;
  background-color: #e0e0e0;
  border-radius: 4px;
  color: #666;
  margin-left: 8px;
}

.context-variable-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 8px;
  color: #4285f4;
  font-size: 14px;
}

.token-count {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #666;
}

.token-icon {
  color: #4285f4;
}

.system-prompt-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background-color: #f5f5f5;
  border-radius: 8px 8px 0 0;
  margin-top: 8px;
}

.jinja-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
}

.variable-insert {
  color: #4285f4;
  cursor: pointer;
  font-size: 14px;
}

.expand-button {
  color: #666;
  cursor: pointer;
}

.system-prompt-input {
  background-color: #f5f5f5;
  border-radius: 0 0 8px 8px;
  padding: 0 8px 8px 8px;
}

.prompt-textarea {
  width: 100%;
  min-height: 80px;
  border: none;
  resize: none;
  font-size: 14px;
  color: #666;
  outline: none;
  background-color: #f5f5f5;
  padding: 8px;
}

.add-message-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 8px;
  color: #666;
  font-size: 14px;
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-label {
  font-size: 14px;
  color: #666;
}

.toggle-switch {
  appearance: none;
  width: 36px;
  height: 20px;
  background-color: #ccc;
  border-radius: 10px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s;
}

.toggle-switch:checked {
  background-color: #4285f4;
}

.toggle-switch::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  top: 2px;
  left: 2px;
  transition: transform 0.3s;
}

.toggle-switch:checked::before {
  transform: translateX(16px);
}
