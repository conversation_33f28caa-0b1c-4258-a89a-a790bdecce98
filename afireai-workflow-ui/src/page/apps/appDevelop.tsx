import React from "react";
import { ReactFlowProvider } from '@xyflow/react';
// import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';

import Menu from './menu.tsx';
import Develop from './develop.tsx';

const AppDevelop = () => {
  return (
    <div className="content-container">
      <Menu />
      <div className="workspace">
        <ReactFlowProvider>
          <Develop />
        </ReactFlowProvider>
      </div>
    </div>
  );
};

export default AppDevelop;
