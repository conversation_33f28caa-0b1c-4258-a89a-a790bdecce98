import React, { useEffect, useState, useRef } from "react";
import { ReactFlowProvider } from '@xyflow/react';
import { useParams } from 'react-router-dom';
import { getAppById, AppResponse } from '../../services/appService';
import { showSuccess, showError } from '../../utils/toast';
import { AppProvider } from '../../contexts/AppContext';

import Menu from './menu.tsx';
import Workflow from './workflow.tsx';

const AppWorkflow = () => {
  const { id } = useParams<{ id: string }>();
  const [appInfo, setAppInfo] = useState<AppResponse | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // 使用useRef来跟踪API调用，防止在严格模式下重复调用
  const hasCalledApi = useRef<{[key: string]: boolean}>({});

  useEffect(() => {
    const fetchAppInfo = async () => {
      if (!id) return;

      // 检查是否已经为当前ID调用过API
      if (hasCalledApi.current[id]) {
        console.log(`API already called for app_id: ${id}, skipping...`);
        return;
      }

      // 标记为已调用
      hasCalledApi.current[id] = true;

      try {
        setIsLoading(true);
        console.log(`Fetching app info for app_id: ${id}`);

        // 调用API获取应用信息
        const response = await getAppById(id);
        console.log('App info response:', response);

        // 设置应用信息
        setAppInfo(response);

        // 显示成功消息
        // showSuccess("アプリ情報を正常に取得しました");
      } catch (error) {
        console.error("Failed to fetch app info:", error);
        showError(`アプリ情報の取得に失敗しました: ${error instanceof Error ? error.message : '未知のエラー'}`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAppInfo();
  }, [id]);

  return (
    <AppProvider>
      <div className="content-container">
        {isLoading ? (
          <div className="loading-container">読み込み中...</div>
        ) : (
          <>
            <Menu appInfo={appInfo} />
            <div className="workspace">
              <ReactFlowProvider>
                <Workflow appInfo={appInfo} />
              </ReactFlowProvider>
            </div>
          </>
        )}
      </div>
    </AppProvider>
  );
};

export default AppWorkflow;