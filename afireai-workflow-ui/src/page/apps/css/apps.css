.categoryRoot {
    width: 100%;
    height: 100%;
    background: #f5f6fa;
    overflow: auto;
}

.categoryContainer {
    max-width: 1200px;
    margin: 0 auto;
    padding: 32px 24px 0 24px;
}

.categoryTabs {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
}

.categoryTabButton {
    padding: 8px 20px;
    border-radius: 2px;
    border: none;
    background: #f5f6fa;
    color: #726f6f;
    font-weight: 200;
    font-size: 14px;
    cursor: pointer;
    box-shadow: none;
    /* transition: all 0.2s; */
}

.categoryTabButton:hover {
    background: #ffffff;
}

.categoryTabButtonActive {
    background: #e0e7ff;
    color: #1d4ed8;
    font-weight: 700;
    box-shadow: 0 2px 8px #e0e7ff;
}

.searchBar {
    margin-bottom: 0px;
}

.searchInput {
    width: 220px;
    height: 40px;
    padding: 8px 16px;
    border-radius: 4px;
    border: 1px solid #d1d5db;
    font-size: 14px;
    outline: none;
    background: #fff;
    box-sizing: border-box;
    line-height: 24px;
}

.appList {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
}

.noApp {
    color: #888;
    font-size: 18px;
}

.appCard {
    width: 260px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px #e5e7eb;
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    cursor: pointer;
    transition: box-shadow 0.2s;
    position: relative;
    /* 添加相对定位，以便菜单按钮可以绝对定位 */
}

.appCard:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.createAppCard {
    border: 1px dashed #ccc;
    background-color: #f9f9f9;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.loading {
    padding: 20px;
    text-align: center;
    color: #666;
    font-size: 16px;
}

.categoryIcon {
    font-size: 20px;
}

.appIcon {
    font-size: 22px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    /* margin-bottom: 8px; */
}

.appIconImage {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 4px;
}

.createIcon {
    font-size: 32px;
    color: #999;
}

.default-app-icon {
    font-size: 24px;
    color: #2d3436;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.appTitle {
    font-weight: 700;
    font-size: 14px;
    white-space: normal;
    overflow-wrap: anywhere;
}

.appDesc {
    color: #555;
    font-size: 12px;
    white-space: normal;
    overflow-wrap: anywhere;
}

.appTag {
    font-size: 12px;
    color: #888;
    margin-top: 4px;
}


/* 菜单按钮样式 */

.appMenuContainer {
    position: absolute;
    top: 10px;
    right: 10px;
}

.appMenuButton {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    font-size: 20px;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.appMenuButton:hover {
    background-color: rgba(0, 0, 0, 0.05);
}


/* 菜单弹出框样式 */

.appMenu {
    position: absolute;
    top: 30px;
    right: 0;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    min-width: 120px;
}

.appMenuItem {
    display: block;
    width: 100%;
    text-align: left;
    padding: 8px 12px;
    background: none;
    border: none;
    cursor: pointer;
    color: #333;
    font-size: 12px;
}

.appMenuItem:hover {
    background-color: #f5f5f5;
}


/* 删除确认对话框样式 */

.deleteConfirmOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.deleteConfirmDialog {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.deleteConfirmDialog h3 {
    margin-top: 0;
    font-size: 18px;
}

.deleteConfirmDialog p {
    color: #666;
    margin-bottom: 20px;
}

.deleteConfirmButtons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.cancelButton {
    padding: 8px 16px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.deleteButton {
    padding: 8px 16px;
    background-color: #ff4d4f;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.categoryTabContent {
    display: flex;
    align-items: center;
}