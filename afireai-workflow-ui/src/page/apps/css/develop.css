/* 开发页面样式 */


/* 弹出框样式 */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-container {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.close-button {
    background: none;
    border: none;
    font-size: 16px;
    color: #666;
    cursor: pointer;
}

.modal-content {
    padding: 20px;
}

.modal-description {
    margin-bottom: 20px;
    color: #555;
    font-size: 14px;
    line-height: 1.5;
}

.modal-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-start;
}

.modal-footer.center {
    justify-content: center;
}

.create-key-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: white;
    border: 1px solid #ddd;
    color: #333;
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
}

.create-key-button:hover {
    background-color: #f5f5f5;
}

.api-keys-table {
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 80px;
    background-color: #f5f5f5;
    padding: 10px;
    font-weight: 600;
    font-size: 13px;
    color: #555;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 80px;
    padding: 10px;
    border-top: 1px solid #eee;
    font-size: 13px;
    align-items: center;
}

.key-column,
.date-column,
.actions-column {
    padding: 0 5px;
}

.icon-button {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    padding: 5px;
}

.delete-button {
    color: #ff4d4f;
}

.api-key-display {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 10px 15px;
    margin: 20px 0;
}

.api-key-value {
    flex: 1;
    font-family: monospace;
    font-size: 14px;
    color: #333;
    word-break: break-all;
}

.copy-key-button {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 16px;
    padding: 5px;
}

.ok-button {
    background-color: white;
    border: 1px solid #ddd;
    color: #333;
    padding: 8px 30px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
}

.api-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f5f5f5;
    padding: 8px 15px;
    border-radius: 4px;
    margin-bottom: 30px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.api-server-info {
    display: flex;
    align-items: center;
}

.api-label {
    font-size: 13px;
    color: #666;
    margin-right: 10px;
}

.api-url {
    font-size: 13px;
    color: #333;
    margin-right: 10px;
}

.copy-button {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    font-size: 14px;
}

.api-status {
    display: flex;
    align-items: center;
}

.status-badge {
    background-color: #e6f7e6;
    color: #28a745;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 15px;
}

.api-key-button {
    background-color: transparent;
    border: 1px solid #ddd;
    color: #333;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.key-icon {
    font-size: 12px;
}

.menu-button {
    position: absolute;
    top: 0;
    right: 0;
    background: none;
    border: none;
    color: #666;
    font-size: 16px;
    cursor: pointer;
}

.develop-container {
    width: 100%;
    max-width: 1200px;
    margin: 0;
    padding: 30px;
    color: #333;
    background-color: #fff;
    overflow-y: auto;
    height: 100%;
    display: flex;
    position: relative;
}

.content-sidebar {
    width: 280px;
    min-width: 280px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-right: 30px;
    height: fit-content;
    position: sticky;
    top: 30px;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.close-sidebar-button {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
}

.sidebar-menu {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.sidebar-item {
    padding: 0;
    margin: 0;
}

.sidebar-item a {
    display: block;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
}

.sidebar-item a:hover {
    background-color: #f5f5f5;
}

.main-content {
    flex: 1;
    min-width: 0;
}

.develop-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    position: relative;
}

.divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 30px 0;
    width: 100%;
}

.develop-header h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.develop-header p {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.develop-section {
    margin-bottom: 40px;
}

.develop-section h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    color: #333;
}

.develop-section p {
    margin-bottom: 15px;
    line-height: 1.6;
    font-size: 14px;
    color: #444;
}

.develop-section code {
    background-color: #f5f7fa;
    padding: 2px 5px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 13px;
    color: #333;
}

.develop-section strong {
    font-weight: var(--font-weight-bold);
}

.code-block {
    background-color: #1e1e1e;
    border-radius: 4px;
    overflow: hidden;
    margin: 15px 0;
    width: 100%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.code-header {
    background-color: #2d2d2d;
    padding: 8px 15px;
    color: #e0e0e0;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
}

.code-content {
    padding: 15px;
    overflow-x: auto;
}

.code-content pre {
    margin: 0;
    color: #e0e0e0;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;
}

.endpoint {
    margin-bottom: 30px;
    padding: 0;
    background-color: transparent;
}

.endpoint h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.endpoint-details {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.endpoint-method {
    background-color: #666;
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-weight: 600;
    font-size: 13px;
    margin-right: 10px;
}

.endpoint-path {
    font-family: monospace;
    font-size: 14px;
    color: #333;
}

.endpoint h4 {
    font-size: 14px;
    font-weight: 500;
    margin: 20px 0 10px;
    color: #333;
}

.params-table {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;
    width: 100%;
}

.param-row {
    display: grid;
    grid-template-columns: 150px 100px 80px 1fr;
    /* 使用固定宽度确保列对齐 */
    border-bottom: 1px solid #e0e0e0;
    width: 100%;
}

.param-row:last-child {
    border-bottom: none;
}

.param-row-header {
    background-color: #f5f5f5;
    font-weight: 500;
}

.param-name,
.param-type,
.param-required,
.param-desc {
    padding: 10px;
    font-size: 13px;
    color: #333;
}

.param-name {
    font-family: monospace;
}

.param-desc {
    white-space: normal;
    overflow: visible;
}

.post-button {
    background-color: #e6f3ff;
    /* 浅蓝色背景 */
    color: #007bff;
    /* 蓝色文字 */
    border: 1px solid #b3d9ff;
    /* 浅蓝色边框 */
    padding: 2px 4px;
    /* 内边距 */
    font-size: 12px;
    /* 字体大小 */
    font-weight: 600;
    /* 字体粗细 */
    border-radius: 10px;
    /* 圆角，创建胶囊形状 */
    cursor: pointer;
    /* 鼠标悬停时显示指针 */
    transition: all 0.3s ease;
    /* 平滑过渡效果 */
    display: inline-block;
    text-align: center;
    min-width: 50px;
    &:hover {
        background-color: #cce7ff;
        /* 鼠标悬停时的背景颜色 */
        border-color: #80c7ff;
        /* 鼠标悬停时的边框颜色 */
    }
}

.get-button {
    background-color: #cfedd4;
    /* 浅蓝色背景 */
    color: #2f7120;
    /* 蓝色文字 */
    border: 1px solid #609e64;
    /* 浅蓝色边框 */
    padding: 2px 4px;
    /* 内边距 */
    font-size: 12px;
    /* 字体大小 */
    font-weight: 600;
    /* 字体粗细 */
    border-radius: 10px;
    /* 圆角，创建胶囊形状 */
    cursor: pointer;
    /* 鼠标悬停时显示指针 */
    transition: all 0.3s ease;
    /* 平滑过渡效果 */
    display: inline-block;
    text-align: center;
    min-width: 50px;
    &:hover {
        background-color: #b3e8bc;
        /* 鼠标悬停时的背景颜色 */
        border-color: #429247;
        /* 鼠标悬停时的边框颜色 */
    }
}

@media (max-width: 768px) {
    .develop-container {
        width: 90%;
        margin: 0 auto;
        padding: 15px;
    }
    .api-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    .api-status {
        width: 100%;
        justify-content: space-between;
    }
    .param-row {
        grid-template-columns: 1fr;
    }
    .param-row.header {
        display: none;
    }
    .param-name,
    .param-type,
    .param-required,
    .param-desc {
        padding: 8px;
        border-bottom: 1px solid #e0e0e0;
    }
    .param-name::before {
        content: "パラメータ: ";
        font-weight: 600;
    }
    .param-type::before {
        content: "タイプ: ";
        font-weight: 600;
    }
    .param-required::before {
        content: "必須: ";
        font-weight: 600;
    }
    .param-desc::before {
        content: "説明: ";
        font-weight: 600;
    }
}