import React, { useState } from 'react';
import './css/develop.css';
import { FaRegCopy, FaEllipsisH, FaTimes, FaPlus, FaClipboard, FaKey, FaTimes as FaClose } from 'react-icons/fa';

import { copyText } from "../../utils/clipboard";

const Develop = () => {
  const apiUrl = 'https://api.afireai.com/v1';
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [showNewKeyModal, setShowNewKeyModal] = useState(false);
  const [apiKeys, setApiKeys] = useState<Array<{key: string, created: string, lastUsed: string | null}>>([]);
  const [newApiKey, setNewApiKey] = useState('');
  const [copied, setCopied] = useState(false)


  // ★ コピー対象を引数 text で受け取る
  const handleCopy = async (text: string) => {
    const ok = await copyText(text)
    setCopied(ok)
    setTimeout(() => setCopied(false), 1500)
  }
    const handleCreateNewKey = () => {
    // 生成一个随机の API 密钥
    const randomKey = `app-${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
    setNewApiKey(randomKey);

    // 添加到密钥リスト
    const now = new Date();
    const formattedDate = `${now.getMonth() + 1}/${now.getDate()}/${now.getFullYear()} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')} ${now.getHours() >= 12 ? 'PM' : 'AM'}`;

    setApiKeys([...apiKeys, {
      key: randomKey,
      created: formattedDate,
      lastUsed: null
    }]);

    setShowApiKeyModal(false);
    setShowNewKeyModal(true);
  };

  const handleCloseNewKeyModal = () => {
    setShowNewKeyModal(false);
    setShowApiKeyModal(true);
  };

  const handleCopyApiKey = () => {
    navigator.clipboard.writeText(newApiKey);
    // 可以添加一个コピー成功的提示
  };

  const [showSidebar, setShowSidebar] = useState(true);

  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  const sidebarItems = [
    { id: 'execute', label: 'ワークフローを実行' },
    { id: 'details', label: 'ワークフロー実行詳細を取得' },
    // { id: 'stop', label: '生成を停止' },
    // { id: 'upload', label: 'ファイルアップロード' },
    // { id: 'logs', label: 'ワークフローログを取得' },
    // { id: 'info', label: 'アプリケーションの基本情報を取得' },
    // { id: 'params', label: 'アプリケーションのパラメータ情報を取得' },
    // { id: 'webapp', label: 'アプリのWebApp設定を取得' }
  ];

  return (
    <div className="develop-container">
      {showSidebar && (
        <div className="content-sidebar">
          <div className="sidebar-header">
            <h3>内容</h3>
            <button className="close-sidebar-button" onClick={toggleSidebar}>
              <FaClose />
            </button>
          </div>
          <ul className="sidebar-menu">
            {sidebarItems.map(item => (
              <li key={item.id} className="sidebar-item">
                <a href={`#${item.id}`}>{item.label}</a>
              </li>
            ))}
          </ul>
        </div>
      )}
      <div className="main-content">
        <div className="api-header">
        <div className="api-server-info">
          <span className="api-label">APIサーバー</span>
          <span className="api-url">{apiUrl}</span>
          <button className="copy-button" aria-label="コピー" onClick={() => handleCopy(apiUrl) }><FaRegCopy /></button>
          {copied && <span> ✓コピーしました</span>}
        </div>
        <div className="api-status">
          <span className="status-badge">稼働中</span>
            /workflows/run
        </div>
      </div>

      <div className="develop-header">
        <h1>ワークフローアプリAPI</h1>
        <p>ワークフローアプリケーションは、セクションをサポートしていません。</p>
        <button className="menu-button"><FaEllipsisH /></button>
      </div>

      <div className="divider"></div>

      <div className="develop-section">
        <h2>ベースURL</h2>
        <div className="code-block">
          <div className="code-header">
            <span>コード</span>
          </div>
          <div className="code-content">
            <pre>{apiUrl}</pre>
          </div>
        </div>
      </div>

      <div className="divider"></div>

      <div className="develop-section">
        <h2>認証</h2>
        <p>
          サービスAPIは <code>X-API-Key</code> 認証を使用します。
          <strong>APIキーの漏洩を防ぐため、APIキーはクライアント側で共有せず、サーバー側で保存することを強くお勧めします。</strong>
        </p>
        <p>すべてのAPIリクエストにおいて、以下のように <code>X-API-Key</code> HTTPヘッダーにAPIキーを含めてください：</p>

        <div className="code-block">
          <div className="code-header">
            <span>コード</span>
          </div>
          <div className="code-content">
            <pre>X-API-Key: {'{API_KEY}'}</pre>
          </div>
        </div>
      </div>

      <div className="divider"></div>

      <div className="develop-section">
        <h2 className=''>API</h2>
        <div className="endpoint">
          <h3 id="execute">ワークフローを実行</h3>
          <div className="endpoint-details">
            <div className="endpoint-method post-button">POST</div> {/* 确保使用 .post-button 类 */}
            <div className="endpoint-path">/workflows/run/{ '{workflow_id}' }</div>
          </div>
          <p>ワークフローを実行します。ワークフローを実行する前に、保存が必要です。</p>

          <h4>パスパラメータ
          </h4>
          <div className="params-table">
            <div className="param-row param-row-header">
              <div className="param-name">パラメータ</div>
              <div className="param-type">タイプ</div>
              <div className="param-required">必須</div>
              <div className="param-desc">説明</div>
            </div>
            <div className="param-row">
              <div className="param-name">workflow_id</div>
              <div className="param-type">string</div>
              <div className="param-required">はい</div>
              <div className="param-desc">ワークフローID</div>
            </div>

          </div>

          <h4>レスポンス</h4>
          <div className="code-block">
            <div className="code-header">
              <span>レスポンス例</span>
            </div>
            <div className="code-content">
              <pre>{`{
  "workflow_id": "1b5927e9-51a5-4eec-b21a-eaff943cf307",
  "workflow_run_id": "6df825ca-9dc3-4cb5-99ef-04f7270c8706",
  "status": "RUNNING"
}`}</pre>
            </div>
          </div>
        </div>

        <div className="endpoint">
          <h3 id="details">ワークフロー実行詳細を取得</h3>
          <div className="endpoint-details">
            <div className="endpoint-method get-button">GET</div>
            <div className="endpoint-path">/workflows/run/{ '{workflow_id}' }/{ '{workflow_run_id}' }</div>

          </div>
          <p>ワークフロー実行IDをもとに、各ワークフロータスクの現在の実行結果を取得します。</p>

          <h4>パスパラメータ</h4>
          <div className="params-table">
            <div className="param-row param-row-header">
              <div className="param-name">パラメータ</div>
              <div className="param-type">タイプ</div>
              <div className="param-required">必須</div>
              <div className="param-desc">説明</div>
            </div>
            <div className="param-row">
              <div className="param-name">workflow_id</div>
              <div className="param-type">string</div>
              <div className="param-required">はい</div>
              <div className="param-desc">ワークフローID</div>
            </div>

            <div className="param-row">
              <div className="param-name">workflow_run_id</div>
              <div className="param-type">string</div>
              <div className="param-required">はい</div>
              <div className="param-desc">これはワークフローのRUNIDです。ワークフローを実行したレスポンスに含まれます。</div>
            </div>

          </div>

          <h4>レスポンス</h4>
          <div className="code-block">
            <div className="code-header">
              <span>レスポンス例</span>
            </div>
            <div className="code-content">
              <pre>{`{
	
	"app_id": "1b5927e9-51a5-4eec-b21a-eaff943cf307",
	"workflow_run_id": "6df825ca-9dc3-4cb5-99ef-04f7270c8706",
	"type": "API",
	"status": "SUCCESS",
	"started_at": "2025-05-30T10 : 58 : 37",
	"finished_at": "2025-05-30T10 : 59 : 02",
	"nodes" : [
		{
			"node_id": "dataset-grep-1747895059866",
			 "node_ver": "1.0",
			 "status": "SUCCESS",
			 "started_at": "2025-05-30T10 : 58 : 46",...
		},

		{
			"node_id": "start-1747754742304",
			 "node_ver": "1.0",
			 "status": "SUCCESS",
			 "started_at": "2025-05-30T10 : 58 : 40",…
		},
		{
			"node_id": "http-request-1747895076616",
			 "node_ver": "1.0",
			 "status": "SUCCESS",
			 "started_at": "2025-05-30T10 : 58 : 53",…
		},
	]					
}`}</pre>
            </div>
          </div>
        </div>
      </div>

      {/* API キーモーダル */}
      {showApiKeyModal && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h2>APIシークレットキー</h2>
              <button className="close-button" onClick={() => setShowApiKeyModal(false)}>
                <FaTimes />
              </button>
            </div>
            <div className="modal-content">
              <p className="modal-description">
                APIの悪用を防ぐために、APIキーを保護してください。フロントエンドのコードで平文として使用しないでください。 :)
              </p>

              {apiKeys.length > 0 && (
                <div className="api-keys-table">
                  <div className="table-header">
                    <div className="key-column">シークレットキー</div>
                    <div className="date-column">作成日時</div>
                    <div className="date-column">最終使用日時</div>
                    <div className="actions-column"></div>
                  </div>

                  {apiKeys.map((key, index) => (
                    <div className="table-row" key={index}>
                      <div className="key-column">{key.key.substring(0, 10)}...{key.key.substring(key.key.length - 10)}</div>
                      <div className="date-column">{key.created}</div>
                      <div className="date-column">{key.lastUsed || 'なし'}</div>
                      <div className="actions-column">
                        <button className="icon-button"><FaRegCopy /></button>
                        <button className="icon-button delete-button"><FaTimes /></button>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              <div className="modal-footer">
                <button className="create-key-button" onClick={handleCreateNewKey}>
                  <FaPlus />
                  <span>新しいシークレットキーを作成</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 新しいAPIキーモーダル */}
      {showNewKeyModal && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h2>APIシークレットキー</h2>
              <button className="close-button" onClick={handleCloseNewKeyModal}>
                <FaTimes />
              </button>
            </div>
            <div className="modal-content">
              <p className="modal-description">
                このキーを安全でアクセス可能な場所に保管してください。
              </p>

              <div className="api-key-display">
                <div className="api-key-value">{newApiKey}</div>
                <button className="copy-key-button" onClick={handleCopyApiKey}>
                  <FaClipboard />
                </button>
              </div>

              <div className="modal-footer center">
                <button className="ok-button" onClick={handleCloseNewKeyModal}>
                  OK
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default Develop;
