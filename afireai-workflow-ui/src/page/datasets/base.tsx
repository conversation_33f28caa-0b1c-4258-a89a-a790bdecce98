import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { FaArrowLeft } from "react-icons/fa";
import { createKnowledge } from "../../services/knowledgeService";
import "./css/create.css";

const DatasetBase = () => {
  const navigate = useNavigate();
  const [name, setName] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleBack = () => {
    navigate("/datasets");
  };

  const handleNext = async () => {
    if (!name.trim()) {
      setError("名称を入力してください");
      return;
    }
    setError("");
    setLoading(true);
    try {
      const res = await createKnowledge({ kb_name: name });
      const kbId = res.kb_id;
      navigate(`/datasets/${kbId}/create`);
    } catch (e: any) {
      setError(e.message || "作成に失敗しました");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="datasetCreateRoot">
      <div className="datasetCreateContainer">
        <div className="datasetCreateTopBar">
          <button className="datasetCreateBackButton" onClick={handleBack}>
            <FaArrowLeft />
            <span>ナレッジベース</span>
          </button>
          <div className="datasetCreateSteps">
            <div className="datasetCreateStepItem">
              <div className="datasetCreateStepBadge active">Step1</div>
              <div className="datasetCreateStepText active">データソース</div>
            </div>
            <div className="datasetCreateStepLine"></div>
            <div className="datasetCreateStepItem">
              <div className="datasetCreateStepBadge inactive">Step2</div>
              <div className="datasetCreateStepText">設定と実施</div>
            </div>
            <div className="datasetCreateStepLine"></div>
            <div className="datasetCreateStepItem">
              <div className="datasetCreateStepBadge inactive">Step3</div>
              <div className="datasetCreateStepText">完了</div>
            </div>
          </div>
        </div>
        <div className="datasetCreateContent" style={{ maxWidth: 480, margin: "60px auto" }}>
          <h2 className="datasetCreateTitle">ナレッジベースの作成</h2>
          <div style={{ marginBottom: 24 }}>
            <div className="kbModalLabel">ナレッジベースの名称</div>
            <input
              className="kbModalInput"
              type="text"
              placeholder="名称を入力してください"
              value={name}
              onChange={e => setName(e.target.value)}
              disabled={loading}
            />
          </div>
          {error && <div className="kbModalError">{error}</div>}
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <button className="datasetCreateNextButton" onClick={handleNext} disabled={loading}>
              {loading ? "作成中..." : "次へ"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DatasetBase; 