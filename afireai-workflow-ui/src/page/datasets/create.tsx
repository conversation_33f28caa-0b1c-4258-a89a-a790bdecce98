import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import "./css/create.css";
import { FaArrowLeft, FaUpload, FaFileAlt, FaInfoCircle, FaTrashAlt, FaFilePdf } from "react-icons/fa";
import { createKnowledge, uploadKnowledgeFiles, listKnowledgeFiles, deleteKnowledgeFile, KnowledgeFile } from "../../services/knowledgeService";

const DatasetCreate = () => {
  const navigate = useNavigate();
  const { kb_id } = useParams(); // 获取路由参数
  const kbId = kb_id as string;  // 统一变量名
  const [isDragging, setIsDragging] = useState(false);
  const [createEmpty, setCreateEmpty] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [kbName, setKbName] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // 文件上传与列表
  const [fileList, setFileList] = useState<KnowledgeFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState("");
  const [dragActive, setDragActive] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState<string | null>(null); // 删除确认对话框状态

  // 获取文件列表
  const fetchFiles = async () => {
    try {
      const files = await listKnowledgeFiles(kbId);
      setFileList(files);
    } catch (e: any) {
      setUploadError(e.message || "ファイル一覧取得失敗");
    }
  };
  useEffect(() => { fetchFiles(); }, []);

  // 上传文件
  const handleFilesUpload = async (files: FileList | File[]) => {
    if (!files || files.length === 0) return;
    setUploading(true);
    setUploadError("");
    try {
      await uploadKnowledgeFiles(kbId, Array.from(files));
      await fetchFiles();
    } catch (e: any) {
      setUploadError(e.message || "アップロード失敗");
    } finally {
      setUploading(false);
    }
  };

  // 拖拽事件
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(true);
  };
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);
  };
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFilesUpload(e.dataTransfer.files);
    }
  };
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFilesUpload(e.target.files);
      e.target.value = "";
    }
  };

  // 删除文件 - 显示确认对话框
  const handleDeleteFile = (fileId: string) => {
    setDeleteConfirmOpen(fileId); // 打开删除确认对话框
  };

  // 处理删除确认
  const handleDeleteConfirm = async (fileId: string) => {
    try {
      setUploading(true);
      await deleteKnowledgeFile(fileId);
      await fetchFiles();
    } catch (e: any) {
      setUploadError(e.message || "削除失敗");
    } finally {
      setDeleteConfirmOpen(null); // 关闭删除确认对话框
      setUploading(false);
    }
  };

  // 处理删除取消
  const handleDeleteCancel = () => {
    setDeleteConfirmOpen(null); // 关闭删除确认对话框
  };

  // 文件类型icon
  const getFileIcon = (fileType: string) => {
    if (fileType.toLowerCase().includes('pdf')) return <FaFilePdf style={{ color: '#e53935', fontSize: 28 }} />;
    return <FaFileAlt style={{ color: '#2563eb', fontSize: 28 }} />;
  };

  // 文件大小格式化
  const formatSize = (size: number) => {
    if (!size) return '';
    if (size >= 1024 * 1024) return (size / 1024 / 1024).toFixed(2) + 'MB';
    if (size >= 1024) return (size / 1024).toFixed(2) + 'KB';
    return size + 'B';
  };

  // 返回上一页
  const handleBack = () => {
    navigate("/datasets");
  };

  // 处理下一步
  const handleNext = () => {
    navigate(`/datasets/${kbId}/extract`);
  };

  // 弹窗提交
  const handleCreateKb = async () => {
    if (!kbName.trim()) {
      setError("名称を入力してください");
      return;
    }
    setLoading(true);
    setError("");
    try {
      await createKnowledge({ kb_name: kbName });
      setShowModal(false);
      setKbName("");
      // 可选：刷新列表或跳转
    } catch (e: any) {
      setError(e.message || "作成に失敗しました");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="datasetCreateRoot">
      <div className="datasetCreateContainer">
        {/* 顶部导航和步骤指示器 */}
        <div className="datasetCreateTopBar">
          <button className="datasetCreateBackButton" onClick={handleBack}>
            <FaArrowLeft />
            <span>ナレッジベース</span>
          </button>

          <div className="datasetCreateSteps">
            <div className="datasetCreateStepItem">
              <div className="datasetCreateStepBadge active">Step1</div>
              <div className="datasetCreateStepText active">データソース</div>
            </div>
            <div className="datasetCreateStepLine"></div>
            <div className="datasetCreateStepItem">
              <div className="datasetCreateStepBadge inactive">Step2</div>
              <div className="datasetCreateStepText">設定と実施</div>
            </div>
            <div className="datasetCreateStepLine"></div>
            <div className="datasetCreateStepItem">
              <div className="datasetCreateStepBadge inactive">Step3</div>
              <div className="datasetCreateStepText">完了</div>
            </div>
          </div>
        </div>

        {/* 主要内容 */}
        <div className="datasetCreateContent">
          <h2 className="datasetCreateTitle">データソース</h2>

          {/* 文件上传区 */}
          <div className="fileUploadSection">
            <h3 className="fileUploadTitle">テキストファイルをアップロード</h3>
            <div
              className={`fileDropzone${dragActive ? ' active' : ''}`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <div className="fileDropzoneContent">
                <span style={{ fontSize: 24, color: '#999', marginRight: 8 }}>↑</span>
                <span style={{ color: '#666' }}>ファイルまたはフォルダをドラッグアンドドロップする</span>
                <input
                  type="file"
                  id="fileInput"
                  className="fileInput"
                  multiple
                  onChange={handleFileSelect}
                  style={{ display: 'none' }}
                />
                <label htmlFor="fileInput" className="fileBrowseBtn">参照</label>
              </div>
              <div className="fileDropzoneFormats">
                TXT, MARKDOWN, PDF, ... 1つあたりの最大サイズは10MBです。
              </div>
              {uploadError && <div className="fileUploadError">{uploadError}</div>}
              {uploading && <div className="fileUploading">アップロード中...</div>}
            </div>
          </div>

          {/* 文件列表区 */}
          {fileList.length > 0 && (
            <div className="fileListSection">
              {/* <h3 className="fileListTitle">テキストファイルをアップロード</h3> */}
              <div className="fileList">
                {fileList.map(file => (
                  <div className="fileCard" key={file.file_id}>
                    <div className="fileCardIcon">{getFileIcon(file.file_type)}</div>
                    <div className="fileCardInfo">
                      <div className="fileCardName">{file.file_name}</div>
                      <div className="fileCardMeta">{file.file_type.toUpperCase()} ・ {formatSize(file.file_size)}</div>
                    </div>
                    <button className="fileCardDelete" onClick={() => handleDeleteFile(file.file_id)} title="削除">
                      <FaTrashAlt />
                    </button>
                    
                    {/* 削除確認ダイアログ */}
                    {deleteConfirmOpen === file.file_id && (
                      <div className="deleteConfirmOverlay">
                        <div className="deleteConfirmDialog">
                          <h3>ファイルを削除しますか？</h3>
                          <p>この操作は元に戻せません。</p>
                          <div className="deleteConfirmButtons">
                            <button
                              className="cancelButton"
                              onClick={(e) => {
                                e.stopPropagation(); // 阻止イベント冒泡
                                handleDeleteCancel();
                              }}
                            >
                              キャンセル
                            </button>
                            <button
                              className="deleteButton"
                              onClick={(e) => {
                                e.stopPropagation(); // 阻止イベント冒泡
                                handleDeleteConfirm(file.file_id);
                              }}
                            >
                              削除
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 底部按钮 */}
          <div className="datasetCreateActions">
            <div className="datasetCreateCheckboxContainer">
            </div>
            <button className="datasetCreateNextButton" onClick={handleNext}>
              次へ
            </button>
          </div>
        </div>
      </div>

    </div>
  );
};

export default DatasetCreate;
