.datasetCreateRoot {
    width: 100%;
    height: 100%;
    background: #f5f6fa;
    overflow: auto;
}

.datasetCreateContainer {
    width: 100%;
    margin: 0 auto;
    padding: 20px;
}


/* 顶部导航和步骤指示器 */

.datasetCreateTopBar {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 20px;
    width: 100%;
    padding: 10px 20px;
    background-color: #f5f6fa;
}

.datasetCreateBackButton {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    margin-left: 0;
}

.datasetCreateBackButton:hover {
    color: #4a90e2;
}


/* 步骤指示器 */

.datasetCreateSteps {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    position: absolute;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 50%;
}

.datasetCreateStepItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    margin: 0 5px;
}

.datasetCreateStepBadge {
    width: auto;
    height: 24px;
    padding: 0 10px;
    border-radius: 12px;
    background-color: #e0e0e0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 11px;
}

.datasetCreateStepBadge.active {
    background-color: #4a90e2;
    color: white;
}

.datasetCreateStepBadge.inactive {
    background-color: #e0e0e0;
    color: #666;
}

.datasetCreateStepText {
    font-size: 11px;
    color: #666;
}

.datasetCreateStepText.active {
    color: #4a90e2;
    font-weight: bold;
}

.datasetCreateStepLine {
    flex: 1;
    height: 1px;
    background-color: #e0e0e0;
    margin: 0 5px;
    margin-bottom: 4px;
    max-width: 60px;
}


/* 主要内容 */

.datasetCreateContent {
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    width: 50%;
    margin: 0 auto;
}

.datasetCreateTitle {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
    text-align: left;
}


/* 数据源选项 */

.datasetCreateOptions {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.datasetCreateOption {
    width: 100%;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.2s;
}

.datasetCreateOption:hover {
    border-color: #4a90e2;
    background-color: #f5f9ff;
}

.datasetCreateOptionIcon {
    font-size: 20px;
    color: #4a90e2;
}

.datasetCreateOptionText {
    font-size: 14px;
    color: #333;
}


/* 文件上传区域 */

.datasetCreateUploadSection {
    margin-top: 30px;
}

.datasetCreateSubtitle {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
    text-align: left;
}

.datasetCreateDropzone {
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    padding: 30px;
    text-align: left;
    transition: all 0.2s;
    background-color: #f9f9f9;
    position: relative;
}

.datasetCreateDropzone.dragging {
    border-color: #4a90e2;
    background-color: #f5f9ff;
}

.datasetCreateDropzoneContent {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
}

.datasetCreateDropzoneIcon {
    font-size: 24px;
    color: #999;
}

.datasetCreateDropzoneText {
    font-size: 14px;
    color: #666;
    margin: 0;
    text-align: left;
}

.datasetCreateDropzoneHighlight {
    color: #4a90e2;
    font-weight: bold;
}

.datasetCreateDropzoneFormats {
    font-size: 12px;
    color: #999;
    max-width: 600px;
    margin: 0;
    text-align: left;
}

.datasetCreateFileInput {
    display: none;
}

.datasetCreateBrowseButton {
    display: inline-block;
    background-color: #4a90e2;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    margin-top: 10px;
    align-self: flex-start;
}

.datasetCreateBrowseButton:hover {
    background-color: #3a80d2;
}


/* 底部按钮 */

.datasetCreateActions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
}

.datasetCreateCheckboxContainer {
    display: flex;
    align-items: center;
    gap: 8px;
}

.datasetCreateCheckbox {
    width: 16px;
    height: 16px;
}

.datasetCreateCheckboxLabel {
    font-size: 14px;
    color: #333;
}

.datasetCreateNextButton {
    background-color: #4a90e2;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
}

.datasetCreateNextButton:hover {
    background-color: #3a80d2;
}

.kbModalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(30, 41, 59, 0.25);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.kbModal {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 32px rgba(0, 0, 0, 0.12);
    width: 420px;
    max-width: 90vw;
    padding: 32px 32px 24px 32px;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 18px;
}

.kbModalHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
}

.kbModalTitle {
    font-size: 20px;
    font-weight: bold;
    color: #222;
}

.kbModalClose {
    background: none;
    border: none;
    font-size: 28px;
    color: #888;
    cursor: pointer;
    line-height: 1;
}

.kbModalDesc {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 8px;
}

.kbModalLabel {
    font-size: 14px;
    color: #222;
    margin-bottom: 4px;
}

.kbModalInput {
    width: 100%;
    padding: 12px 14px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    font-size: 15px;
    margin-bottom: 8px;
    background: #f9fafb;
    outline: none;
    transition: border 0.2s;
}

.kbModalInput:focus {
    border-color: #2563eb;
}

.kbModalError {
    color: #e11d48;
    font-size: 13px;
    margin-bottom: 4px;
}

.kbModalActions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 8px;
}

.kbModalCancel {
    background: #fff;
    border: 1px solid #e5e7eb;
    color: #222;
    border-radius: 8px;
    padding: 8px 22px;
    font-size: 15px;
    cursor: pointer;
    transition: background 0.2s, border 0.2s;
}

.kbModalCancel:hover {
    background: #f3f4f6;
    border-color: #cbd5e1;
}

.kbModalCreate {
    background: #2563eb;
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 8px 22px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}

.kbModalCreate:disabled {
    background: #93c5fd;
    cursor: not-allowed;
}

.kbModalCreate:not(:disabled):hover {
    background: #1d4ed8;
}

/* 削除確認ダイアログのスタイル */
.deleteConfirmOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.deleteConfirmDialog {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.deleteConfirmDialog h3 {
    margin-top: 0;
    font-size: 18px;
}

.deleteConfirmDialog p {
    color: #666;
    margin-bottom: 20px;
}

.deleteConfirmButtons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.cancelButton {
    padding: 8px 16px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.deleteButton {
    padding: 8px 16px;
    background-color: #ff4d4f;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}