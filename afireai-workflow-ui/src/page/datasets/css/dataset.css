.datasetRoot {
    width: 100%;
    height: 100%;
    background: #f5f6fa;
    overflow: auto;
}

.datasetContainer {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}


/* 标签页样式 */

.datasetTabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
}

.datasetTabButton {
    padding: 10px 20px;
    background: transparent;
    border: none;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    position: relative;
}

.datasetTabButtonActive {
    color: #4a90e2;
    font-weight: 600;
}

.datasetTabButtonActive::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #4a90e2;
}


/* 过滤和搜索区域 */

.datasetFilterBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.datasetFilterOptions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.datasetCheckboxLabel {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.datasetCheckbox {
    margin-right: 8px;
}

.datasetTagSelector {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: white;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.datasetDropdownIcon {
    font-size: 10px;
    color: #999;
}

.datasetSearchAndApi {
    display: flex;
    align-items: center;
    gap: 15px;
}

.datasetSearchBar {
    position: relative;
}

.datasetSearchIcon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 14px;
}

.datasetSearchInput {
    padding: 8px 10px 8px 35px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    width: 200px;
}

.datasetApiButton {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.datasetApiIcon {
    font-size: 12px;
    color: #666;
}


/* 主要内容区域 */

.datasetContent {
    display: flex;
    gap: 24px;
}


/* 左侧创建区域 */

.datasetCreateSection {
    width: 260px;
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.datasetCreateButton {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    height: 36px;
}

.datasetCreateIcon {
    font-size: 14px;
    color: #666;
}

.datasetCreateDescription {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 4px;
}

.datasetExternalButton {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 0;
    background: transparent;
    border: none;
    font-size: 12px;
    color: #4a90e2;
    cursor: pointer;
    text-align: left;
}

.datasetExternalIcon {
    font-size: 12px;
}


/* 右侧数据集列表 */

.datasetList {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.datasetLoading,
.datasetError,
.datasetEmpty {
    width: 100%;
    padding: 40px;
    text-align: center;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.datasetLoading {
    color: #4a90e2;
}

.datasetError {
    color: #e74c3c;
}

.datasetEmpty {
    color: #95a5a6;
}

.datasetCard {
    width: 260px;
    height: 160px;
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    display: flex;
    flex-direction: column;
}

.datasetCardHeader {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.datasetCardIcon {
    font-size: 22px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #f0f4ff;
    border-radius: 8px;
    color: #4a6cf7;
}

.datasetCardTitle {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.datasetCardDescription {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 6px;
}

.datasetCardDetails {
    margin-top: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.datasetCardDetails span {
    font-size: 11px;
    color: #888;
}

.datasetCardFooter {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
}

.datasetCardTag {
    display: flex;
    align-items: center;
    color: #888;
    font-size: 12px;
}

.datasetCardTagIcon {
    margin-right: 4px;
    font-size: 14px;
}

.datasetCardActions {
    color: #888;
    font-size: 16px;
    cursor: pointer;
    position: relative;
}

.datasetMenu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 10;
    min-width: 120px;
    overflow: hidden;
}

.datasetMenuItem {
    padding: 8px 12px;
    font-size: 13px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.datasetMenuItem:hover {
    background: #f5f6fa;
}

.datasetMenuItemDelete {
    color: #e74c3c;
}

/* 删除确认对话框 */
.deleteConfirmOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.deleteConfirmDialog {
    background: white;
    border-radius: 8px;
    padding: 20px;
    width: 400px;
    max-width: 90%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.deleteConfirmDialog h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
}

.deleteConfirmDialog p {
    margin-bottom: 20px;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

.deleteConfirmButtons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.cancelButton {
    padding: 8px 16px;
    background: #f5f6fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.deleteButton {
    padding: 8px 16px;
    background: #e74c3c;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    color: white;
    cursor: pointer;
}

.deleteConfirmButtons button.deleteButton {
  background-color: #e53935;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.deleteConfirmButtons button.deleteButton:hover {
  background-color: #c62828;
}

/* 更新ポップアップスタイル */
.updatePopupOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.updatePopupDialog {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.updatePopupDialog h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
  font-size: 18px;
}

.updateFormGroup {
  margin-bottom: 16px;
}

.updateFormGroup label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.updateFormGroup input,
.updateFormGroup textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.updateFormGroup textarea {
  resize: vertical;
  min-height: 80px;
}

.updatePopupButtons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.updatePopupButtons button {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.updatePopupButtons button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.updatePopupButtons button.cancelButton {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.updatePopupButtons button.updateButton {
  background-color: #1976d2;
  color: white;
  border: none;
}

.updatePopupButtons button.updateButton:hover:not(:disabled) {
  background-color: #1565c0;
}

.datasetMenuItem.datasetMenuItemUpdate {
  color: #1976d2;
}

.datasetMenuItem.datasetMenuItemUpdate:hover {
  background-color: #e3f2fd;
}
