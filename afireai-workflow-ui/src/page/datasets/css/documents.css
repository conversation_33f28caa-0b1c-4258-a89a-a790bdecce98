.documentsRoot {
    width: 100%;
    height: 100vh;
    background: #f5f6fa;
    display: flex;
    overflow: hidden;
}


/* Sidebar */

.documentsSidebar {
    width: 300px;
    background: white;
    border-right: 1px solid #e5e7eb;
    padding: 20px;
    overflow-y: auto;
    flex-shrink: 0;
}

.sidebarKnowledgeInfo {
    margin-bottom: 24px;
}

.sidebarKnowledgeIcon {
    width: 48px;
    height: 48px;
    background-color: #3b82f6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin-bottom: 12px;
}

.sidebarKnowledgeDetails h3 {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 4px 0;
}

.sidebarKnowledgeDetails p {
    font-size: 12px;
    color: #6b7280;
    margin: 0 0 8px 0;
}

.sidebarKnowledgeDescription {
    font-size: 14px;
    color: #374151;
    line-height: 1.4;
    margin-bottom: 16px;
}

.sidebarNoApps {
    padding: 16px 0;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 16px;
}

.sidebarNoApps p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sidebarMenu {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.sidebarMenuItem {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    transition: all 0.2s;
}

.sidebarMenuItem:hover {
    background-color: #f3f4f6;
}

.sidebarMenuItem.active {
    background-color: #dbeafe;
    color: #1d4ed8;
}

.sidebarMenuItem.active svg {
    color: #1d4ed8;
}


/* Main Content */

.documentsMainContent {
    flex: 1;
    overflow-y: auto;
}

.documentsContainer {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    min-height: 100vh;
}


/* Header */

.documentsHeader {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.documentsHeaderLeft {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.documentsBackButton {
    background: none;
    border: none;
    color: #6b7280;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
}

.documentsBackButton:hover {
    background-color: #f3f4f6;
}

.documentsTitle {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0;
}

.documentsSubtitle {
    font-size: 12px;
    color: #6b7280;
    margin: 8px 0 0 0;
    line-height: 1.5;
    /* max-width: 600px; */
}

.documentsLearnMore {
    color: #3b82f6;
    text-decoration: none;
}

.documentsLearnMore:hover {
    text-decoration: underline;
}

.documentsHeaderRight {
    display: flex;
    gap: 12px;
}

.documentsAddButton {
    display: flex;
    align-items: center;
    gap: 2px;
    background-color: #3b82f6;
    color: white;
    border: none;
    padding: 4px 10px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    font-weight: 500;
}

.documentsAddButton:hover {
    background-color: #2563eb;
}


/* Knowledge Base Info */

.documentsKnowledgeInfo {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 24px;
}

.documentsKnowledgeIcon {
    width: 40px;
    height: 40px;
    background-color: #3b82f6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.documentsKnowledgeDetails {
    flex: 1;
}

.documentsKnowledgeDetails h3 {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 4px 0;
}

.documentsKnowledgeDetails p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

.documentsKnowledgeActions {
    display: flex;
    gap: 8px;
}


/* Search and Actions */

.documentsActions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.documentsSearchContainer {
    position: relative;
    width: 300px;
}

.documentsSearchIcon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 14px;
}

.documentsSearchInput {
    width: 100%;
    padding: 8px 12px 8px 36px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
}

.documentsSearchInput:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}


/* Table */

.documentsTable {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 16px;
}

.documentsTableHeader {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr 100px;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.documentsTableHeaderCell {
    padding: 12px 16px;
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.documentsTableBody {
    background-color: white;
}

.documentsTableRow {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr 100px;
    border-bottom: 1px solid #f3f4f6;
    align-items: center;
}

.documentsTableRow:hover {
    background-color: #f9fafb;
}

.documentsTableRow:last-child {
    border-bottom: none;
}

.documentsTableCell {
    padding: 8px 12px;
    font-size: 14px;
    color: #374151;
}

.fileNameColumn {
    min-width: 0;
}

.documentsFileName {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.documentsFileName span {
    font-weight: 500;
    color: #111827;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.documentsFileSize {
    font-size: 12px;
    color: #6b7280;
}

.documentsStatus {
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.documentsActionButton {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
}

.documentsActionButton:hover {
    background-color: #f3f4f6;
    color: #374151;
}


/* Toggle Switch */

.documentsToggleSwitch {
    position: relative;
    display: inline-block;
}

.documentsToggleInput {
    opacity: 0;
    width: 0;
    height: 0;
}

.documentsToggleLabel {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 19px;
    background-color: #3b82f6;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.documentsToggleLabel:before {
    content: "";
    position: absolute;
    top: 2px;
    left: 2px;
    width: 15px;
    height: 15px;
    background-color: white;
    border-radius: 20%;
    transition: transform 0.3s;
}

.documentsToggleInput:checked+.documentsToggleLabel {
    background-color: #3b82f6;
}

.documentsToggleInput:not(:checked)+.documentsToggleLabel {
    background-color: #d1d5db;
}

.documentsToggleInput:not(:checked)+.documentsToggleLabel:before {
    transform: translateX(0);
}

.documentsToggleInput:checked+.documentsToggleLabel:before {
    transform: translateX(20px);
}


/* Empty State */

.documentsEmptyState {
    padding: 40px;
    text-align: center;
    color: #6b7280;
}


/* Pagination */

.documentsPagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    font-size: 14px;
    color: #6b7280;
}

.documentsPaginationControls {
    display: flex;
    gap: 8px;
}

.documentsPaginationControls button {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.documentsPaginationControls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.documentsPaginationControls button:hover:not(:disabled) {
    background-color: #f3f4f6;
}