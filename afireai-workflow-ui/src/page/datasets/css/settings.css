.datasetSettingsRoot {
    width: 100%;
    height: 100%;
    background: #f5f6fa;
    overflow: auto;
}

.datasetSettingsContainer {
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.datasetSettingsTopBar {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 20px;
    width: 100%;
    padding: 10px 20px;
    background-color: #f5f6fa;
}

.datasetSettingsBackButton {
    display: flex;
    align-items: center;
    gap: 6px;
    background: none;
    border: none;
    color: #333;
    font-size: 13px;
    cursor: pointer;
    padding: 0;
    margin-left: 0;
}

.datasetSettingsBackButton:hover {
    color: #4a90e2;
}


/* 步骤指示器 */

.datasetSettingsSteps {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    position: absolute;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 50%;
}

.datasetSettingsStepItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    margin: 0 5px;
}

.datasetSettingsStepBadge {
    width: auto;
    height: 20px;
    padding: 0 8px;
    border-radius: 10px;
    background-color: #e0e0e0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 3px;
    font-size: 10px;
}

.datasetSettingsStepBadge.active {
    background-color: #4a90e2;
    color: white;
}

.datasetSettingsStepText {
    font-size: 10px;
    color: #666;
}

.datasetSettingsStepText.active {
    color: #4a90e2;
    font-weight: bold;
}

.datasetSettingsStepLine {
    flex: 1;
    height: 1px;
    background-color: #e0e0e0;
    margin: 0 5px;
    margin-bottom: 3px;
    max-width: 50px;
}


/* 主要内容 */

.datasetSettingsContent {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    width: 50%;
    margin: 0 auto;
}


/* 成功信息 */

.datasetSettingsSuccessSection {
    text-align: center;
    margin-bottom: 20px;
}

.datasetSettingsSuccessTitle {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}

.datasetSettingsSuccessDescription {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}


/* 名称设置 */

.datasetSettingsNameSection {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
}

.datasetSettingsNameIcon {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    background-color: #f0e6d2;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.datasetSettingsNameInputContainer {
    flex: 1;
}

.datasetSettingsNameLabel {
    font-size: 13px;
    font-weight: 500;
    color: #333;
    margin-bottom: 6px;
}

.datasetSettingsNameDisplay {
    width: 100%;
    min-height: 36px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px;
    font-size: 13px;
    background-color: #f5f5f5;
    color: #333;
    display: flex;
    align-items: center;
}


/* 完成信息 */

.datasetSettingsCompletedSection {
    margin-bottom: 20px;
}

.datasetSettingsCompletedTitle {
    font-size: 15px;
    font-weight: bold;
    color: #333;
    margin-bottom: 12px;
}


/* 文件项 */

.datasetSettingsFileItem {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: white;
}

.datasetSettingsFileItemIcon {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-right: 12px;
}

.datasetSettingsFileItemIcon.pdf {
    background-color: #ffebee;
    color: #f44336;
}

.datasetSettingsFileItemName {
    flex: 1;
    font-size: 13px;
    color: #333;
}

.datasetSettingsFileItemStatus {
    color: #4caf50;
    font-size: 16px;
}

.datasetSettingsFileItemStatusIcon {
    background-color: #e8f5e9;
    border-radius: 50%;
    padding: 2px;
}


/* 信息列表 */

.datasetSettingsInfoSection {
    margin-bottom: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
}

.datasetSettingsInfoItem {
    display: flex;
    justify-content: space-between;
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.datasetSettingsInfoItem:last-child {
    border-bottom: none;
}

.datasetSettingsInfoLabel {
    font-size: 13px;
    color: #666;
}

.datasetSettingsInfoValue {
    font-size: 13px;
    color: #333;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.datasetSettingsInfoRow {
    display: flex;
    width: 100%;
    justify-content: space-between;
}

.datasetSettingsInfoColumn {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.datasetSettingsInfoTag {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 11px;
}

.datasetSettingsInfoTag.orange {
    background-color: #fff3e0;
    color: #ff9800;
}

.datasetSettingsInfoTag.purple {
    background-color: #f3e5f5;
    color: #9c27b0;
}

.datasetSettingsInfoTag.blue {
    background-color: #e3f2fd;
    color: #2196f3;
}

.datasetSettingsInfoTagIcon {
    font-size: 12px;
}


/* API按钮 */

.datasetSettingsApiSection {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.datasetSettingsApiButton {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    height: 36px;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.datasetSettingsApiButton:hover {
    background-color: #f5f5f5;
}

.datasetSettingsDocButton {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    height: 36px;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
}

.datasetSettingsDocButton:hover {
    background-color: #3a80d2;
}

.datasetSettingsApiIcon {
    font-size: 14px;
    margin-right: 4px;
}