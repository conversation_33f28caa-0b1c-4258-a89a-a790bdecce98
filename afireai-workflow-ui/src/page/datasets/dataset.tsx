import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import "./css/dataset.css";
import { FaPlus, FaSearch, FaExternalLinkAlt, FaFolder, FaTag, FaEllipsisH, FaTrash, FaEdit } from "react-icons/fa";
import { listKnowledgeBases, deleteKnowledgeBase, updateKnowledgeBase, KnowledgeResponse, UpdateKnowledgeRequest } from "../../services/knowledgeService";

const Dataset = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("knowledge");
  const [showAll, setShowAll] = useState(true);
  const [searchText, setSearchText] = useState("");
  const [selectedTag, setSelectedTag] = useState("all");
  const [datasets, setDatasets] = useState<KnowledgeResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [menuOpen, setMenuOpen] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState<string | null>(null);
  const [updatePopupOpen, setUpdatePopupOpen] = useState<string | null>(null);
  const [updateFormData, setUpdateFormData] = useState<UpdateKnowledgeRequest>({ kb_name: "", kb_description: "" });
  const [isUpdating, setIsUpdating] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const handleCreateClick = () => {
    navigate("/datasets/base");
  };

  // APIからナレッジベースのリストを取得する
  const fetchKnowledgeBases = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await listKnowledgeBases();
      setDatasets(data);
    } catch (err: any) {
      setError(err.message || 'ナレッジベースの取得に失敗しました');
      console.error('Failed to fetch knowledge bases:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // コンポーネントのマウント時にデータを取得
  useEffect(() => {
    fetchKnowledgeBases();
    
    // 点击外部关闭菜单
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMenuOpen(null);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // 处理菜单点击
  const handleMenuClick = (e: React.MouseEvent, datasetId: string) => {
    e.stopPropagation(); // 阻止事件冒泡，防止触发卡片点击
    setMenuOpen(menuOpen === datasetId ? null : datasetId);
  };
  
  // 处理删除点击
  const handleDeleteClick = (e: React.MouseEvent, datasetId: string) => {
    e.stopPropagation(); // 阻止事件冒泡，防止触发卡片点击
    setMenuOpen(null);
    setDeleteConfirmOpen(datasetId);
  };
  
  // 处理删除确认
  const handleDeleteConfirm = async (datasetId: string) => {
    try {
      await deleteKnowledgeBase(datasetId);
      // 删除成功后刷新列表
      fetchKnowledgeBases();
      setDeleteConfirmOpen(null);
    } catch (err: any) {
      setError(err.message || 'ナレッジベースの削除に失敗しました');
      console.error('Failed to delete knowledge base:', err);
      setDeleteConfirmOpen(null);
    }
  };
  
  // 处理删除取消
  const handleDeleteCancel = () => {
    setDeleteConfirmOpen(null);
  };
  
  // 处理更新点击
  const handleUpdateClick = (e: React.MouseEvent, dataset: KnowledgeResponse) => {
    e.stopPropagation(); // 阻止事件冒泡，防止触发卡片点击
    setMenuOpen(null);
    setUpdateFormData({
      kb_name: dataset.kb_name,
      kb_description: dataset.kb_description || ""
    });
    setUpdatePopupOpen(dataset.kb_id);
  };
  
  // 处理更新表单输入变化
  const handleUpdateInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setUpdateFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // 处理更新确认
  const handleUpdateConfirm = async () => {
    if (!updatePopupOpen) return;
    
    try {
      setIsUpdating(true);
      await updateKnowledgeBase(updatePopupOpen, updateFormData);
      // 更新成功后刷新列表
      fetchKnowledgeBases();
      setUpdatePopupOpen(null);
    } catch (err: any) {
      setError(err.message || 'ナレッジベースの更新に失敗しました');
      console.error('Failed to update knowledge base:', err);
    } finally {
      setIsUpdating(false);
    }
  };
  
  // 处理更新取消
  const handleUpdateCancel = () => {
    setUpdatePopupOpen(null);
  };
  
  // 検索テキストに基づいてデータをフィルタリングする
  const filteredDatasets = datasets.filter(dataset => {
    // 検索テキストが空の場合はすべてのデータを表示
    if (!searchText) return true;
    
    // 検索テキストがナレッジベース名または説明に含まれているかチェック
    const searchLower = searchText.toLowerCase();
    return (
      dataset.kb_name.toLowerCase().includes(searchLower) ||
      (dataset.kb_description && dataset.kb_description.toLowerCase().includes(searchLower))
    );
  });

  return (
    <div className="datasetRoot">
      {deleteConfirmOpen && (
        <div className="deleteConfirmOverlay">
          <div className="deleteConfirmDialog">
            <h3>ナレッジベースの削除</h3>
            <p>このナレッジベースを削除してもよろしいですか？この操作は元に戻せません。</p>
            <div className="deleteConfirmButtons">
              <button className="cancelButton" onClick={handleDeleteCancel}>キャンセル</button>
              <button className="deleteButton" onClick={() => handleDeleteConfirm(deleteConfirmOpen)}>削除</button>
            </div>
          </div>
        </div>
      )}

      {/* 更新ポップアップ */}
      {updatePopupOpen && (
        <div className="updatePopupOverlay">
          <div className="updatePopupDialog">
            <h3>ナレッジベースの更新</h3>
            <div className="updateFormGroup">
              <label htmlFor="kb_name">ナレッジ名称</label>
              <input
                type="text"
                id="kb_name"
                name="kb_name"
                value={updateFormData.kb_name}
                onChange={handleUpdateInputChange}
                placeholder="ナレッジ名称を入力"
              />
            </div>
            <div className="updateFormGroup">
              <label htmlFor="kb_description">ナレッジ概要</label>
              <textarea
                id="kb_description"
                name="kb_description"
                value={updateFormData.kb_description}
                onChange={handleUpdateInputChange}
                placeholder="ナレッジの概要を入力"
                rows={4}
              />
            </div>
            <div className="updatePopupButtons">
              <button className="cancelButton" onClick={handleUpdateCancel} disabled={isUpdating}>キャンセル</button>
              <button className="updateButton" onClick={handleUpdateConfirm} disabled={isUpdating}>
                {isUpdating ? '更新中...' : '更新'}
              </button>
            </div>
          </div>
        </div>
      )}
      <div className="datasetContainer">
        {/* 顶部导航栏 */}
        <div className="datasetTabs">
          <button
            onClick={() => setActiveTab("knowledge")}
            className={
              activeTab === "knowledge"
                ? "datasetTabButton datasetTabButtonActive"
                : "datasetTabButton"
            }
          >
            ナレッジベース
          </button>
        </div>

        {/* 过滤和搜索区域 */}
        <div className="datasetFilterBar">
          <div className="datasetFilterOptions">
            <label className="datasetCheckboxLabel">
              <input
                type="checkbox"
                checked={showAll}
                onChange={() => setShowAll(!showAll)}
                className="datasetCheckbox"
              />
              ナレッジベース全体
            </label>

          </div>

          <div className="datasetSearchAndApi">
            <div className="datasetSearchBar">
              <FaSearch className="datasetSearchIcon" />
              <input
                type="text"
                placeholder="検索"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                className="datasetSearchInput"
              />
            </div>

            <button className="datasetApiButton">
              <FaExternalLinkAlt className="datasetApiIcon" />
              外部ナレッジベース連携API
            </button>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="datasetContent">


          {/* 右侧数据集列表 */}
          <div className="datasetList">

          {/* 左侧创建区域 */}
          <div className="datasetCreateSection">
            <button className="datasetCreateButton" onClick={handleCreateClick}>
              <FaPlus className="datasetCreateIcon" />
              ナレッジベースを作成
            </button>
            <div className="datasetCreateDescription">
              独自のテキストデータをインポートする。LLMコンテキストが最適化のためにWebhookをクリアリアルタイムデータを書き込むことができます。
            </div>

          </div>

            {isLoading ? (
              <div className="datasetLoading">読み込み中...</div>
            ) : error ? (
              <div className="datasetError">{error}</div>
            ) : filteredDatasets.length === 0 ? (
              <div className="datasetEmpty">
                {datasets.length === 0 ? 'ナレッジベースがありません' : '検索結果がありません'}
              </div>
            ) : (
              filteredDatasets.map((dataset) => (
                <div 
                  key={dataset.kb_id} 
                  className="datasetCard"
                  onClick={() => navigate(`/datasets/${dataset.kb_id}`)}
                >
                  <div className="datasetCardHeader">
                    <div className="datasetCardIcon"><FaFolder /></div>
                    <div className="datasetCardTitle">{dataset.kb_name}</div>
                  </div>
                  <div className="datasetCardDescription">
                    {dataset.kb_description ? (dataset.kb_description.length > 36 ? `${dataset.kb_description.substring(0, 36)}...` : dataset.kb_description) : 'No description'}
                    <div className="datasetCardDetails">
                      <span>作成日: {new Date(dataset.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                  <div className="datasetCardFooter">
                    <div className="datasetCardTag">
                      <FaTag className="datasetCardTagIcon" />
                      <span>タグを追加</span>
                    </div>
                    <div className="datasetCardActions">
                      <div onClick={(e) => handleMenuClick(e, dataset.kb_id)}>
                        <FaEllipsisH />
                      </div>
                      {menuOpen === dataset.kb_id && (
                        <div className="datasetMenu" ref={menuRef}>
                          <div 
                            className="datasetMenuItem datasetMenuItemUpdate"
                            onClick={(e) => handleUpdateClick(e, dataset)}
                          >
                            <FaEdit />
                            <span>更新</span>
                          </div>
                          <div 
                            className="datasetMenuItem datasetMenuItemDelete"
                            onClick={(e) => handleDeleteClick(e, dataset.kb_id)}
                          >
                            <FaTrash />
                            <span>削除</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dataset;
