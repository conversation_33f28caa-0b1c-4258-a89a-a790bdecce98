import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import "./css/documents.css";
import { FaArrowLeft, FaFileAlt, FaFilePdf, FaEllipsisV, FaPlus, FaSearch, FaCog, FaFolder, FaSlidersH } from "react-icons/fa";
import { listKnowledgeFiles, getKnowledgeBase, KnowledgeFile, KnowledgeResponse } from '../../services/knowledgeService';

const DatasetDocuments = () => {
  const navigate = useNavigate();
  const { kb_id } = useParams();
  const kbId = kb_id as string;

  // State
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeResponse | null>(null);
  const [fileList, setFileList] = useState<KnowledgeFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState("");

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      if (!kbId) return;
      
      try {
        setLoading(true);
        const [kbData, filesData] = await Promise.all([
          getKnowledgeBase(kbId),
          listKnowledgeFiles(kbId)
        ]);
        setKnowledgeBase(kbData);
        setFileList(filesData);
      } catch (error) {
        console.error('Failed to fetch data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [kbId]);

  // Helper functions
  const getFileIcon = (fileType: string) => {
    if (fileType.toLowerCase().includes('pdf')) {
      return <FaFilePdf style={{ color: '#e53935', fontSize: 16 }} />;
    }
    return <FaFileAlt style={{ color: '#2563eb', fontSize: 16 }} />;
  };

  const formatFileSize = (size: number) => {
    if (!size) return '0 KB';
    if (size >= 1024 * 1024) return (size / 1024 / 1024).toFixed(1) + ' MB';
    if (size >= 1024) return (size / 1024).toFixed(1) + ' KB';
    return size + ' B';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'done':
      case 'completed':
        return '#4caf50';
      case 'processing':
      case 'ingesting':
        return '#ff9800';
      case 'error':
        return '#f44336';
      default:
        return '#9e9e9e';
    }
  };

  const getStatusText = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'done':
        return '利用可能';
      case 'processing':
      case 'ingesting':
        return '処理中';
      case 'error':
        return 'エラー';
      default:
        return '利用可能';
    }
  };

  const handleFileUpload = () => {
    navigate(`/datasets/${kbId}/create`);
  };

  const handleSettings = () => {
    navigate(`/datasets/${kbId}/extract`);
  };

  // Filter files based on search
  const filteredFiles = fileList.filter(file =>
    file.file_name.toLowerCase().includes(searchText.toLowerCase())
  );

  if (loading) {
    return (
      <div className="documentsRoot">
        <div className="documentsContainer">
          <div style={{ textAlign: 'center', padding: '50px' }}>
            読み込み中...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="documentsRoot">
      {/* Left Sidebar */}
      <div className="documentsSidebar">
        {/* Knowledge Base Info */}
        <div className="sidebarKnowledgeInfo">
          <div className="sidebarKnowledgeIcon">
            <FaFolder />
          </div>
          <div className="sidebarKnowledgeDetails">
            <h3>{knowledgeBase?.kb_name || 'ITツール登録要領.pdf...'}</h3>
            <p>ローカルドキュメント</p>
            <div className="sidebarKnowledgeDescription">
              {knowledgeBase?.kb_description ||
                `Useful for when you want to answer queries about the ${knowledgeBase?.kb_name || 'ITツール登録要領.pdf'}`}
            </div>
          </div>
        </div>

        {/* No Related Apps */}
        <div className="sidebarNoApps">
          <p>関連付けられたアプリはありません</p>
        </div>

        {/* Menu Items */}
        <div className="sidebarMenu">
          <div className="sidebarMenuItem active" onClick={() => navigate(`/datasets/${kbId}/documents`)}>
            <FaFileAlt />
            <span>ドキュメント</span>
          </div>
          <div className="sidebarMenuItem" onClick={handleSettings}>
            <FaSlidersH />
            <span>設定</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="documentsMainContent">
        <div className="documentsContainer">
        {/* Header */}
        <div className="documentsHeader">
          <div className="documentsHeaderLeft">
            <h1 className="documentsTitle">ドキュメント</h1>
            <p className="documentsSubtitle">
              すべてのファイルがここに表示され、ナレッジベース全体が AfireAI の引用付きチャットプラグインを介してリンクされるか、インデックス化されることができます。
            </p>
          </div>
        </div>

        {/* Search and Actions */}
        <div className="documentsActions">
          <div className="documentsSearchContainer">
            <FaSearch className="documentsSearchIcon" />
            <input
              type="text"
              placeholder="検索"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className="documentsSearchInput"
            />
          </div>

          <div className="documentsHeaderRight">
            <button className="documentsAddButton" onClick={handleFileUpload}>
              <FaPlus />
              ファイル追加
            </button>
          </div>

        </div>

        {/* File List Table */}
        <div className="documentsTable">
          <div className="documentsTableHeader">
            <div className="documentsTableHeaderCell fileNameColumn">ファイル名</div>
            <div className="documentsTableHeaderCell">単語数</div>
            <div className="documentsTableHeaderCell">検索回数</div>
            <div className="documentsTableHeaderCell">アップロード時間</div>
            <div className="documentsTableHeaderCell">ステータス</div>
            <div className="documentsTableHeaderCell">アクション</div>
          </div>

          <div className="documentsTableBody">
            {filteredFiles.length === 0 ? (
              <div className="documentsEmptyState">
                <p>ファイルがありません</p>
              </div>
            ) : (
              filteredFiles.map((file) => (
                <div key={file.file_id} className="documentsTableRow">
                  <div className="documentsTableCell fileNameColumn">
                    <div className="documentsFileName">
                      {getFileIcon(file.file_type)}
                      <span>{file.file_name}</span>
                    </div>
                    <div className="documentsFileSize">{formatFileSize(file.file_size)}</div>
                  </div>
                  <div className="documentsTableCell">0</div>
                  <div className="documentsTableCell">0</div>
                  <div className="documentsTableCell">{formatDate(file.created_at)}</div>
                  <div className="documentsTableCell">
                    <span
                      className="documentsStatus"
                      style={{ color: getStatusColor(file.status) }}
                    >
                      ● {getStatusText(file.status)}
                    </span>
                  </div>
                  <div className="documentsTableCell">
                    <div className="documentsToggleSwitch">
                      <input
                        type="checkbox"
                        id={`toggle-${file.file_id}`}
                        className="documentsToggleInput"
                        defaultChecked={true}
                      />
                      <label
                        htmlFor={`toggle-${file.file_id}`}
                        className="documentsToggleLabel"
                      ></label>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Pagination */}
        <div className="documentsPagination">
          <span>1/1</span>
          <div className="documentsPaginationControls">
            <button disabled>10</button>
            <button disabled>25</button>
            <button disabled>50</button>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
};

export default DatasetDocuments;
