import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import "./css/documents.css";
import { FaArrowLeft, FaFileAlt, FaFilePdf, FaEllipsisV, FaPlus, FaSearch, FaCog, FaFolder, FaSlidersH } from "react-icons/fa";
import { listKnowledgeFiles, getKnowledgeBase, KnowledgeFile, KnowledgeResponse } from '../../services/knowledgeService';
import { getAccessToken } from '../../services/authService';

const DatasetDocuments = () => {
  const navigate = useNavigate();
  const { kb_id } = useParams();
  const kbId = kb_id as string;

  // State
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeResponse | null>(null);
  const [fileList, setFileList] = useState<KnowledgeFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState("");

  // Menu and popup states
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [showRenamePopup, setShowRenamePopup] = useState<string | null>(null);
  const [showDeletePopup, setShowDeletePopup] = useState<string | null>(null);
  const [newFileName, setNewFileName] = useState("");

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      if (!kbId) return;
      
      try {
        setLoading(true);
        const [kbData, filesData] = await Promise.all([
          getKnowledgeBase(kbId),
          listKnowledgeFiles(kbId)
        ]);
        setKnowledgeBase(kbData);
        setFileList(filesData);
      } catch (error) {
        console.error('Failed to fetch data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [kbId]);

  // Helper functions
  const getFileIcon = (fileType: string) => {
    if (fileType.toLowerCase().includes('pdf')) {
      return <FaFilePdf style={{ color: '#e53935', fontSize: 16 }} />;
    }
    return <FaFileAlt style={{ color: '#2563eb', fontSize: 16 }} />;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'READY':
        return '#9e9e9e';  // グレー - 処理準備中
      case 'INGESTING':
        return '#ff9800';  // オレンジ - 取り込み中
      case 'DONE':
        return '#4caf50';  // グリーン - 正常終了
      case 'ERROR':
        return '#f44336';  // レッド - エラー
      default:
        return '#9e9e9e';
    }
  };

  const getStatusText = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'READY':
        return '処理準備中';
      case 'INGESTING':
        return '取り込み中';
      case 'DONE':
        return '正常終了';
      case 'ERROR':
        return 'エラー';
      default:
        return '処理準備中';
    }
  };

  const handleFileUpload = () => {
    navigate(`/datasets/${kbId}/create`);
  };

  const handleSettings = () => {
    navigate(`/datasets/${kbId}/extract`);
  };

  const handleToggleValidFlag = async (fileId: string, currentValidFlg: string) => {
    try {
      const newValidFlg = currentValidFlg === "1" ? "0" : "1";

      // API call to update valid_flg
      const response = await fetch(`/api/knowledge-files/${fileId}/valid-flag`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...(getAccessToken() ? { 'Authorization': `Bearer ${getAccessToken()}` } : {})
        },
        body: JSON.stringify({ valid_flg: newValidFlg })
      });

      if (!response.ok) {
        throw new Error('Failed to update file status');
      }

      // Update local state
      setFileList(prevFiles =>
        prevFiles.map(file =>
          file.file_id === fileId
            ? { ...file, valid_flg: newValidFlg }
            : file
        )
      );
    } catch (error) {
      console.error('Error updating file status:', error);
      alert('ファイルステータスの更新に失敗しました');
    }
  };

  // Menu handlers
  const handleMenuToggle = (fileId: string) => {
    setActiveMenu(activeMenu === fileId ? null : fileId);
  };

  const handleRename = (file: KnowledgeFile) => {
    setNewFileName(file.file_name);
    setShowRenamePopup(file.file_id);
    setActiveMenu(null);
  };

  const handleDelete = (fileId: string) => {
    setShowDeletePopup(fileId);
    setActiveMenu(null);
  };

  const handleRenameConfirm = async (fileId: string) => {
    try {
      const response = await fetch(`/api/knowledge-files/${fileId}/rename`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...(getAccessToken() ? { 'Authorization': `Bearer ${getAccessToken()}` } : {})
        },
        body: JSON.stringify({ file_name: newFileName })
      });

      if (!response.ok) {
        throw new Error('Failed to rename file');
      }

      // Update local state
      setFileList(prevFiles =>
        prevFiles.map(file =>
          file.file_id === fileId
            ? { ...file, file_name: newFileName }
            : file
        )
      );

      setShowRenamePopup(null);
      setNewFileName("");
    } catch (error) {
      console.error('Error renaming file:', error);
      alert('ファイル名の変更に失敗しました');
    }
  };

  const handleDeleteConfirm = async (fileId: string) => {
    try {
      const response = await fetch(`/api/knowledge-files/${fileId}`, {
        method: 'DELETE',
        headers: {
          ...(getAccessToken() ? { 'Authorization': `Bearer ${getAccessToken()}` } : {})
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete file');
      }

      // Remove from local state
      setFileList(prevFiles => prevFiles.filter(file => file.file_id !== fileId));
      setShowDeletePopup(null);
    } catch (error) {
      console.error('Error deleting file:', error);
      alert('ファイルの削除に失敗しました');
    }
  };

  // Filter files based on search
  const filteredFiles = fileList.filter(file =>
    file.file_name.toLowerCase().includes(searchText.toLowerCase())
  );

  if (loading) {
    return (
      <div className="documentsRoot">
        <div className="documentsContainer">
          <div style={{ textAlign: 'center', padding: '50px' }}>
            読み込み中...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="documentsRoot">
      {/* Left Sidebar */}
      <div className="documentsSidebar">
        {/* Knowledge Base Info */}
        <div className="sidebarKnowledgeInfo">
          <div className="sidebarKnowledgeIcon">
            <FaFolder />
          </div>
          <div className="sidebarKnowledgeDetails">
            <h3>{knowledgeBase?.kb_name || 'ITツール登録要領.pdf...'}</h3>
            <p>ローカルドキュメント</p>
            <div className="sidebarKnowledgeDescription">
              {knowledgeBase?.kb_description ||
                `Useful for when you want to answer queries about the ${knowledgeBase?.kb_name || 'ITツール登録要領.pdf'}`}
            </div>
          </div>
        </div>

        {/* No Related Apps */}
        <div className="sidebarNoApps">
          <p>関連付けられたアプリはありません</p>
        </div>

        {/* Menu Items */}
        <div className="sidebarMenu">
          <div className="sidebarMenuItem active" onClick={() => navigate(`/datasets/${kbId}/documents`)}>
            <FaFileAlt />
            <span>ドキュメント</span>
          </div>
          <div className="sidebarMenuItem" onClick={handleSettings}>
            <FaSlidersH />
            <span>設定</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="documentsMainContent">
        <div className="documentsContainer">
        {/* Header */}
        <div className="documentsHeader">
          <div className="documentsHeaderLeft">
            <h1 className="documentsTitle">ドキュメント</h1>
            <p className="documentsSubtitle">
              すべてのファイルがここに表示され、ナレッジベース全体が AfireAI の引用付きチャットプラグインを介してリンクされるか、インデックス化されることができます。
            </p>
          </div>
        </div>

        {/* Search and Actions */}
        <div className="documentsActions">
          <div className="documentsSearchContainer">
            <FaSearch className="documentsSearchIcon" />
            <input
              type="text"
              placeholder="検索"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className="documentsSearchInput"
            />
          </div>

          <div className="documentsHeaderRight">
            <button className="documentsAddButton" onClick={handleFileUpload}>
              <FaPlus />
              ファイル追加
            </button>
          </div>

        </div>

        {/* File List Table */}
        <div className="documentsTable">
          <div className="documentsTableHeader">
            <div className="documentsTableHeaderCell fileNameColumn">ファイル名</div>
            <div className="documentsTableHeaderCell">単語数</div>
            <div className="documentsTableHeaderCell">検索回数</div>
            <div className="documentsTableHeaderCell">アップロード時間</div>
            <div className="documentsTableHeaderCell">ステータス</div>
            <div className="documentsTableHeaderCell">有効/無効</div>
            <div className="documentsTableHeaderCell">アクション</div>
            <div className="documentsTableHeaderCell">操作</div>
          </div>

          <div className="documentsTableBody">
            {filteredFiles.length === 0 ? (
              <div className="documentsEmptyState">
                <p>ファイルがありません</p>
              </div>
            ) : (
              filteredFiles.map((file) => (
                <div key={file.file_id} className="documentsTableRow">
                  <div className="documentsTableCell fileNameColumn">
                    <div className="documentsFileName">
                      {getFileIcon(file.file_type)}
                      <span>{file.file_name}</span>
                    </div>
                  </div>
                  <div className="documentsTableCell">0</div>
                  <div className="documentsTableCell">0</div>
                  <div className="documentsTableCell">{formatDate(file.created_at)}</div>
                  <div className="documentsTableCell">
                    <span
                      className="documentsStatus"
                      style={{ color: getStatusColor(file.status) }}
                    >
                      ● {getStatusText(file.status)}
                    </span>
                  </div>
                  <div className="documentsTableCell">
                    <span
                      className="documentsValidStatus"
                      style={{ color: file.valid_flg === "1" ? '#4caf50' : '#f44336' }}
                    >
                      {file.valid_flg === "1" ? '有効' : '無効'}
                    </span>
                  </div>
                  <div className="documentsTableCell">
                    <div className="documentsToggleSwitch">
                      <input
                        type="checkbox"
                        id={`toggle-${file.file_id}`}
                        className="documentsToggleInput"
                        checked={file.valid_flg === "1"}
                        onChange={() => handleToggleValidFlag(file.file_id, file.valid_flg || "0")}
                      />
                      <label
                        htmlFor={`toggle-${file.file_id}`}
                        className="documentsToggleLabel"
                      ></label>
                    </div>
                  </div>
                  <div className="documentsTableCell">
                    <div className="documentsMenuContainer">
                      <button
                        className="documentsMenuButton"
                        onClick={() => handleMenuToggle(file.file_id)}
                      >
                        <FaEllipsisV />
                      </button>
                      {activeMenu === file.file_id && (
                        <div className="documentsDropdownMenu">
                          <button
                            className="documentsDropdownItem"
                            onClick={() => handleRename(file)}
                          >
                            <span>✏️</span>
                            名前を変更
                          </button>
                          <button
                            className="documentsDropdownItem"
                            onClick={() => handleDelete(file.file_id)}
                          >
                            <span>🗑️</span>
                            削除
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Pagination */}
        <div className="documentsPagination">
          <span>1/1</span>
          <div className="documentsPaginationControls">
            <button disabled>10</button>
            <button disabled>25</button>
            <button disabled>50</button>
          </div>
        </div>

        {/* Rename Popup */}
        {showRenamePopup && (
          <div className="documentsPopupOverlay">
            <div className="documentsPopup">
              <h3>名前を変更</h3>
              <input
                type="text"
                value={newFileName}
                onChange={(e) => setNewFileName(e.target.value)}
                className="documentsPopupInput"
                placeholder="ファイル名を入力"
              />
              <div className="documentsPopupButtons">
                <button
                  className="documentsPopupButton cancel"
                  onClick={() => setShowRenamePopup(null)}
                >
                  キャンセル
                </button>
                <button
                  className="documentsPopupButton confirm"
                  onClick={() => handleRenameConfirm(showRenamePopup)}
                >
                  変更
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Popup */}
        {showDeletePopup && (
          <div className="documentsPopupOverlay">
            <div className="documentsPopup">
              <h3>削除確認</h3>
              <p>このファイルを削除してもよろしいですか？</p>
              <div className="documentsPopupButtons">
                <button
                  className="documentsPopupButton cancel"
                  onClick={() => setShowDeletePopup(null)}
                >
                  キャンセル
                </button>
                <button
                  className="documentsPopupButton delete"
                  onClick={() => handleDeleteConfirm(showDeletePopup)}
                >
                  削除
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
      </div>
    </div>
  );
};

export default DatasetDocuments;
