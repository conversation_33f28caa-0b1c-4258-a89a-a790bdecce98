import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import "./css/extract.css";
import { FaArrowLeft, FaSearch, FaFileAlt, FaUndo, FaTrashAlt, FaFilePdf, FaRobot, FaBrain, FaComments } from "react-icons/fa";
import { FiFileText, FiSliders } from "react-icons/fi";
import { BsQuestionCircle, BsGrid3X3, BsTextLeft } from "react-icons/bs";
import { IoMdSwitch } from "react-icons/io";
import { RiRobot2Fill } from "react-icons/ri";
import { uploadKnowledgeFiles, listKnowledgeFiles, deleteKnowledgeFile, processKnowledge, KnowledgeFile, KnowledgeProcessRequest } from '../../services/knowledgeService';
import { GptIcon } from '../../components/afireai-icons';

// 设置配置类型
interface EmbeddingModelConfig {
  value: string;
  label: string;
  icon: string;
  iconLibrary?: string;
}

interface SettingConfig {
  embeddingModels: EmbeddingModelConfig[];
}

// 提示组件
const InfoTooltip = ({ content }: { content: string }) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <span className="tooltipContainer">
      <BsQuestionCircle
        className="infoIcon"
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      />
      {showTooltip && (
        <div className="tooltip">
          {content}
        </div>
      )}
    </span>
  );
};

// 推荐标签组件
const RecommendBadge = () => {
  return <span className="recommendBadge">推奨</span>;
};

const DatasetExtract = () => {
  const navigate = useNavigate();
  const { kb_id } = useParams();
  const kbId = kb_id as string;

  // 基本设置
  const [chunkSize, setChunkSize] = useState("1024");
  const [overlap, setOverlap] = useState("50");
  const [chunkIdentifier, setChunkIdentifier] = useState("\\n\\n");
  const [removeLineBreaks, setRemoveLineBreaks] = useState(true);
  const [removeUrls, setRemoveUrls] = useState(false);

  // 索引方法
  const [indexMethod, setIndexMethod] = useState("highQuality"); // highQuality, economic

  // 嵌入模型
  const [embeddingModel, setEmbeddingModel] = useState("text-embedding-3-large");
  const [showModelDropdown, setShowModelDropdown] = useState(false);
  const [embeddingModels, setEmbeddingModels] = useState<EmbeddingModelConfig[]>([]);

  // 搜索设置
  const [useRerank, setUseRerank] = useState(false);
  const [topK, setTopK] = useState(3);
  const [scoreThreshold, setScoreThreshold] = useState(0.5);

  // 文件上传与列表
  const [fileList, setFileList] = useState<KnowledgeFile[]>([]);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState<string | null>(null); // 删除确认对话框状态
  const [processing, setProcessing] = useState(false); // 処理中状態

  // 读取设置配置
  const loadSettings = async () => {
    try {
      const response = await fetch('/setting.json');
      const settings: SettingConfig = await response.json();
      setEmbeddingModels(settings.embeddingModels);
      // 设置默认值为第一个模型
      if (settings.embeddingModels.length > 0) {
        setEmbeddingModel(settings.embeddingModels[0].value);
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      // 如果加载失败，使用默认配置
      const defaultModels: EmbeddingModelConfig[] = [
        { value: 'text-embedding-3-large', label: 'text-embedding-3-large', icon: 'FaRobot' },
        { value: 'text-embedding-3-small', label: 'text-embedding-3-small', icon: 'FaBrain' },
        { value: 'text-embedding-ada-002', label: 'text-embedding-ada-002', icon: 'FaComments' }
      ];
      setEmbeddingModels(defaultModels);
    }
  };

  // 获取文件列表
  const fetchFiles = async () => {
    try {
      const files = await listKnowledgeFiles(kbId);
      setFileList(files);
    } catch (e: any) {
      alert(e.message || "ファイル一覧取得失敗");
    }
  };

  // 删除文件 - 显示确认对话框
  const handleDeleteFile = (fileId: string) => {
    setDeleteConfirmOpen(fileId); // 打开删除确认对话框
  };

  // 处理删除确认
  const handleDeleteConfirm = async (fileId: string) => {
    try {
      await deleteKnowledgeFile(fileId);
      await fetchFiles();
    } catch (e: any) {
      console.error('Failed to delete file:', e);
    } finally {
      setDeleteConfirmOpen(null); // 关闭删除确认对话框
    }
  };

  // 处理删除取消
  const handleDeleteCancel = () => {
    setDeleteConfirmOpen(null); // 关闭删除确认对话框
  };

  // 文件大小格式化
  const formatSize = (size: number) => {
    if (!size) return '';
    if (size >= 1024 * 1024) return (size / 1024 / 1024).toFixed(2) + 'MB';
    if (size >= 1024) return (size / 1024).toFixed(2) + 'KB';
    return size + 'B';
  };
  
  useEffect(() => { 
    loadSettings();
    fetchFiles(); 
  }, []);


  // 文件类型icon
  const getFileIcon = (fileType: string) => {
    if (fileType.toLowerCase().includes('pdf')) return <FaFilePdf style={{ color: '#e53935', fontSize: 28 }} />;
    return <FaFileAlt style={{ color: '#2563eb', fontSize: 28 }} />;
  };

  // 根据图标名称和库获取图标组件
  const getIconComponent = (iconName: string) => {
      switch (iconName) {
        case 'GptIcon':
          return <GptIcon />;
        default:
          return <GptIcon />;
      }
    
  };

  // 获取当前选中模型的图标
  const getCurrentModelIcon = () => {
    const currentModel = embeddingModels.find(model => model.value === embeddingModel);
    return currentModel ? getIconComponent(currentModel.icon) : <FaRobot />;
  };


  // 处理返回上一步
  const handleBack = () => {
    navigate(`/datasets/${kbId}/create`);
  };

  // 处理下一步
  const handleNext = async () => {
    try {
      setProcessing(true);
      
      const payload: KnowledgeProcessRequest = {
        embedding_model: embeddingModel,
        chunk_identifier: chunkIdentifier,
        chunk_size: parseInt(chunkSize),
        chunk_overlap: parseInt(overlap),
        remove_line_breaks: removeLineBreaks,
        remove_urls: removeUrls
      };
      
      const result = await processKnowledge(kbId, payload);
      
      if (result.success) {
        alert(`処理が完了しました。\n処理されたファイル数: ${result.processed_files}\n生成されたチャンク数: ${result.total_chunks}`);
        navigate("/datasets/settings");
      } else {
        alert(`処理に失敗しました: ${result.message}`);
      }
    } catch (error: any) {
      alert(`エラーが発生しました: ${error.message}`);
    } finally {
      setProcessing(false);
    }
  };

  // 处理预览
  const handlePreview = () => {
    // 预览逻辑
    console.log("预览分块");
  };

  // 处理重置
  const handleReset = () => {
    setChunkIdentifier("\\n\\n");
    setChunkSize("1024");
    setOverlap("50");
    setRemoveLineBreaks(true);
    setRemoveUrls(false);
  };

  // 更新滑动条的活动部分颜色
  React.useEffect(() => {
    const topKSlider = document.querySelector('input[type="range"][min="1"][max="20"]') as HTMLInputElement;
    if (topKSlider) {
      const percent = ((topK - 1) / 19) * 100;
      topKSlider.style.setProperty('--value', `${percent}%`);
    }

    const scoreSlider = document.querySelector('input[type="range"][min="0"][max="1"]') as HTMLInputElement;
    if (scoreSlider) {
      const percent = (scoreThreshold / 1) * 100;
      scoreSlider.style.setProperty('--value', `${percent}%`);
    }
  }, [topK, scoreThreshold]);

  return (
    <div className="datasetExtractRoot">
      {/* 顶部导航和步骤指示器 */}
      <div className="datasetCreateTopBar">
        <button className="datasetCreateBackButton" onClick={handleBack}>
          <FaArrowLeft />
          <span>ナレッジベース</span>
        </button>

        <div className="datasetCreateSteps">
          <div className="datasetCreateStepItem">
            <div className="datasetCreateStepBadge inactive">Step1</div>
            <div className="datasetCreateStepText">データソース</div>
          </div>
          <div className="datasetCreateStepLine"></div>
          <div className="datasetCreateStepItem">
            <div className="datasetCreateStepBadge active">Step2</div>
            <div className="datasetCreateStepText">設定と実施</div>
          </div>
          <div className="datasetCreateStepLine"></div>
          <div className="datasetCreateStepItem">
            <div className="datasetCreateStepBadge inactive">Step3</div>
            <div className="datasetCreateStepText">完了</div>
          </div>
        </div>
      </div>

      <div className="datasetExtractContainer">
        <div className="datasetExtractContent">
          <div className="datasetExtractLeftContent">
            <div className="datasetExtractSection">
              <h2 className="datasetExtractSectionTitle">チャンク設定</h2>

              <div className="datasetExtractCard">
                <div className="datasetExtractCardHeader">
                  <div className="datasetExtractCardIcon blue">
                    <FiFileText />
                  </div>
                  <div className="datasetExtractCardTitle">汎用</div>
                </div>
                <div className="datasetExtractCardDescription">
                  汎用テキスト分割モードです。検索とコン設定と実施に同じチャンクを使用します。
                </div>

                <div className="datasetExtractFormGroup">
                  <div className="datasetExtractFormRow" style={{ display: 'flex', alignItems: 'center', marginBottom: '15px' }}>
                    <div style={{ marginRight: '15px' }}>
                      <div className="datasetExtractFormLabel">
                        チャンク識別子 <InfoTooltip content="区切り文字は、テキストを区切るために使用される文字です。\n\n と \n は、段落と行を区切るために一般的に使用される区切り記号です。" />
                      </div>
                      <div className="datasetExtractFormInput">
                        <input
                          type="text"
                          value={chunkIdentifier}
                          onChange={(e) => setChunkIdentifier(e.target.value)}
                          className="datasetExtractTextInput"
                          style={{ width: '180px' }}
                        />
                      </div>
                    </div>

                    <div style={{ marginRight: '15px' }}>
                      <div className="datasetExtractFormLabel">
                        最大チャンク長
                      </div>
                      <div className="datasetExtractFormInput">
                        <input
                          type="text"
                          value={chunkSize}
                          onChange={(e) => setChunkSize(e.target.value)}
                          className="datasetExtractTextInput"
                        />
                        <span className="datasetExtractInputSuffix">characters</span>
                      </div>
                    </div>

                    <div>
                      <div className="datasetExtractFormLabel">
                        チャンクのオーバーラップ <InfoTooltip content="連続するチャンク間で重複する文字数を指定します。" />
                      </div>
                      <div className="datasetExtractFormInput">
                        <input
                          type="text"
                          value={overlap}
                          onChange={(e) => setOverlap(e.target.value)}
                          className="datasetExtractTextInput"
                        />
                        <span className="datasetExtractInputSuffix">characters</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="datasetExtractFormGroup">
                  <div className="datasetExtractFormLabel">テキストの前処理ルール</div>
                  <div className="datasetExtractCheckboxGroup">
                    <label className="datasetExtractCheckboxLabel">
                      <input
                        type="checkbox"
                        checked={removeLineBreaks}
                        onChange={() => setRemoveLineBreaks(!removeLineBreaks)}
                        className="datasetExtractCheckbox"
                      />
                      連続するスペース、改行、タブを置換する
                    </label>

                    <label className="datasetExtractCheckboxLabel">
                      <input
                        type="checkbox"
                        checked={removeUrls}
                        onChange={() => setRemoveUrls(!removeUrls)}
                        className="datasetExtractCheckbox"
                      />
                      すべてのURLとメールアドレスを削除する
                    </label>
                  </div>
                </div>

                <div className="datasetExtractButtonGroup">
                  <button className="datasetExtractResetButton" onClick={handleReset}>
                  <FaUndo />リセット
                  </button>
                </div>
              </div>
            </div>

            {/* <div className="datasetExtractSection">
              <h2 className="datasetExtractSectionTitle">インデックス方法</h2>
              <div className="datasetExtractIndexMethods">
                <div className="datasetExtractIndexCard selected">
                  <div className="datasetExtractIndexCardHeader">
                    <div className="datasetExtractIndexCardIcon">
                      <FiSliders />
                    </div>
                    <div className="datasetExtractIndexCardTitle">
                    高品質なモデルを使用してドキュメントを処理し、より正確な検索結果を得ることができます。LLMが高品質の回答を生成するのに役立ちます。
                    </div>
                  </div>
                  <div className="datasetExtractIndexCardDescription">
                    
                  </div>
                </div>
              </div>
            </div> */}

            <div className="datasetExtractSection">
              <h2 className="datasetExtractSectionTitle">埋め込みモデル</h2>
              <div className="datasetExtractFormGroup">
                {/* <div className="datasetExtractFormLabel">モデル選択</div> */}
                <div className="datasetExtractFormInput">
                  <div className="datasetExtractCustomSelect">
                    <div className="datasetExtractSelectDisplay" onClick={() => setShowModelDropdown(!showModelDropdown)}>
                      <div className="datasetExtractSelectIcon">
                        {getCurrentModelIcon()}
                      </div>
                      <span>{embeddingModel}</span>
                      <div className="datasetExtractSelectArrow">
                        {showModelDropdown ? '▲' : '▼'}
                      </div>
                    </div>
                    {showModelDropdown && (
                      <div className="datasetExtractSelectDropdown">
                        {embeddingModels.map((model) => (
                          <div 
                            key={model.value}
                            className={`datasetExtractSelectOption ${embeddingModel === model.value ? 'selected' : ''}`}
                            onClick={() => { setEmbeddingModel(model.value); setShowModelDropdown(false); }}
                          >
                            <div className="datasetExtractSelectOptionIcon">
                              {getIconComponent(model.icon)}
                            </div>
                            <span>{model.label}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>


            {/* 底部按钮区域 */}
            <div className="datasetExtractSection">
              <div className="datasetExtractActions">
                <button className="datasetExtractSecondaryButton" onClick={handleBack}>
                  <FaArrowLeft style={{ marginRight: '5px' }} /> 前のステップ
                </button>
                <button 
                  className="datasetExtractPrimaryButton" 
                  onClick={handleNext}
                  disabled={processing || fileList.length === 0}
                >
                  {processing ? '処理中...' : '保存して次へ'}
                </button>
              </div>
            </div>{/* 底部按钮区域 */}

          </div>


          </div>

          {/* 右側の内容 */} 
          <div className="datasetExtractRightContent">
            <div className="datasetExtractPreviewHeader">
              <h2 className="datasetExtractSectionTitle">データソース</h2>

              {/* ファイルリスト表示 */}
              {fileList.length > 0 ? (
                <div className="fileListSection">
                  <div className="fileList">
                    {fileList.map((file) => (
                      <div className="fileCard" key={file.file_id}>
                        <div className="fileCardIcon">{getFileIcon(file.file_type)}</div>
                        <div className="fileCardInfo">
                          <div className="fileCardName">{file.file_name}</div>
                          <div className="fileCardMeta">{file.file_type.toUpperCase()} ・ {formatSize(file.file_size)}</div>
                        </div>
                        <button className="fileCardDelete" onClick={() => handleDeleteFile(file.file_id)} title="削除">
                          <FaTrashAlt />
                        </button>
                        
                        {/* 削除確認ダイアログ */}
                        {deleteConfirmOpen === file.file_id && (
                          <div className="deleteConfirmOverlay">
                            <div className="deleteConfirmDialog">
                              <h3>ファイルを削除しますか？</h3>
                              <p>この操作は元に戻せません。</p>
                              <div className="deleteConfirmButtons">
                                <button
                                  className="cancelButton"
                                  onClick={(e) => {
                                    e.stopPropagation(); // 阻止イベント冒泡
                                    handleDeleteCancel();
                                  }}
                                >
                                  キャンセル
                                </button>
                                <button
                                  className="deleteButton"
                                  onClick={(e) => {
                                    e.stopPropagation(); // 阻止イベント冒泡
                                    handleDeleteConfirm(file.file_id);
                                  }}
                                >
                                  削除
                                </button>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="fileCard">
                  <div className="fileCardIcon">
                    <FaFileAlt />
                  </div>
                  <div className="fileCardInfo">
                    <div className="fileCardName">ファイルがありません</div>
                    <div className="fileCardMeta">検出チャンク数: 0</div>
                  </div>
                </div>
              )}

            </div>
          </div>{/* 右側の内容完了 */}

      </div>





    </div>
  );
};

export default DatasetExtract;
