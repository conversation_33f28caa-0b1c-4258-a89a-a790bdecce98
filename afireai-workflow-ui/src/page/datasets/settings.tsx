import React, { useState, useEffect } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import "./css/settings.css";
import { FaArrowLeft, FaRobot, FaFileAlt, FaExternalLinkAlt, FaCheck, FaFilePdf } from "react-icons/fa";
import { MdOutlineAutoAwesome } from "react-icons/md";
import { IoDocumentTextOutline } from "react-icons/io5";
import { getKnowledgeBase, listKnowledgeFiles, KnowledgeResponse, KnowledgeFile } from '../../services/knowledgeService';

const DatasetSettings = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { kb_id } = useParams();
  const kbId = kb_id as string;

  // State for dynamic data
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeResponse | null>(null);
  const [fileList, setFileList] = useState<KnowledgeFile[]>([]);
  const [loading, setLoading] = useState(true);

  // Get processing data from navigation state
  const processingData = location.state?.processingData;

  // Fetch knowledge base and file data
  useEffect(() => {
    const fetchData = async () => {
      if (!kbId) return;

      try {
        setLoading(true);
        const [kbData, filesData] = await Promise.all([
          getKnowledgeBase(kbId),
          listKnowledgeFiles(kbId)
        ]);
        setKnowledgeBase(kbData);
        setFileList(filesData);
      } catch (error) {
        console.error('Failed to fetch data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [kbId]);

  // Helper functions
  const getFileIcon = (fileType: string) => {
    if (fileType.toLowerCase().includes('pdf')) return <FaFilePdf style={{ color: '#e53935', fontSize: 28 }} />;
    return <FaFileAlt style={{ color: '#2563eb', fontSize: 28 }} />;
  };

  const formatPreprocessingRules = (removeLineBreaks?: boolean, removeUrls?: boolean): string[] => {
    const rules: string[] = [];
    if (removeLineBreaks) rules.push("連続するスペース、改行、タブを置換する");
    if (removeUrls) rules.push("すべてのURLとメールアドレスを削除する");
    return rules.length > 0 ? rules : ["なし"];
  };

  if (loading) {
    return (
      <div className="datasetSettingsRoot">
        <div className="datasetSettingsContainer">
          <div style={{ textAlign: 'center', padding: '50px' }}>
            読み込み中...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="datasetSettingsRoot">
      <div className="datasetSettingsContainer">
      {/* 顶部导航和步骤指示器 */}
      <div className="datasetCreateTopBar">
        <button className="datasetCreateBackButton" onClick={() => navigate("/datasets")}>
          <FaArrowLeft />
          <span>ナレッジベース</span>
        </button>

        <div className="datasetCreateSteps">
          <div className="datasetCreateStepItem">
            <div className="datasetCreateStepBadge inactive">Step1</div>
            <div className="datasetCreateStepText">データソース</div>
          </div>
          <div className="datasetCreateStepLine"></div>
          <div className="datasetCreateStepItem">
            <div className="datasetCreateStepBadge inactive">Step2</div>
            <div className="datasetCreateStepText">設定と実施</div>
          </div>
          <div className="datasetCreateStepLine"></div>
          <div className="datasetCreateStepItem">
            <div className="datasetCreateStepBadge active">Step3</div>
            <div className="datasetCreateStepText">完了</div>
          </div>
        </div>
      </div>

        <div className="datasetSettingsContent">
          <div className="datasetSettingsSuccessSection">
            <h2 className="datasetSettingsSuccessTitle">
              🎉 ナレッジベースが作成されました
            </h2>
          </div>

          <div className="datasetSettingsCompletedSection">
            <h3 className="datasetSettingsCompletedTitle">埋め込みが完了しました</h3>

            {fileList.map((file) => (
              <div key={file.file_id} className="datasetSettingsFileItem">
                <div className="datasetSettingsFileItemIcon pdf">
                  {getFileIcon(file.file_type)}
                </div>
                <div className="datasetSettingsFileItemName">
                  {file.file_name}
                </div>
                <div className="datasetSettingsFileItemStatus">
                  <FaCheck className="datasetSettingsFileItemStatusIcon" />
                </div>
              </div>
            ))}
          </div>

          <div className="datasetSettingsInfoSection">
            <div className="datasetSettingsInfoItem">
              <div className="datasetSettingsInfoRow">
                <div className="datasetSettingsInfoColumn">
                  <div className="datasetSettingsInfoLabel">チャンク識別子</div>
                  <div className="datasetSettingsInfoValue">
                    {knowledgeBase?.chunk_identifier || "\\n\\n"}
                  </div>
                </div>
                <div className="datasetSettingsInfoColumn">
                  <div className="datasetSettingsInfoLabel">最大なチャンクの長さ</div>
                  <div className="datasetSettingsInfoValue">
                    {knowledgeBase?.chunk_size || 1024}
                  </div>
                </div>

                <div className="datasetSettingsInfoColumn">
                  <div className="datasetSettingsInfoLabel">チャンクのオーバーラップの長さ</div>
                  <div className="datasetSettingsInfoLabel">
                    {knowledgeBase?.chunk_overlap || 50}
                  </div>
                </div>
                
              </div>
            </div>
            <div className="datasetSettingsInfoItem">
              <div className="datasetSettingsInfoLabel">埋め込みモデル</div>
              <div className="datasetSettingsInfoValue">
                <div className="datasetSettingsInfoTag blue">
                  {knowledgeBase?.embedding_model || "text-embedding-3-small"}
                </div>
              </div>
            </div>
            <div className="datasetSettingsInfoItem">
              <div className="datasetSettingsInfoLabel">テキストの前処理ルール</div>
              <div className="datasetSettingsInfoValue">
                {formatPreprocessingRules(
                  processingData?.remove_line_breaks ?? true,
                  processingData?.remove_urls ?? false
                ).map((rule, index) => (
                  <div key={index} className="datasetSettingsInfoTag blue">
                    {rule}
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="datasetSettingsApiSection">
            <button
              className="datasetSettingsDocButton"
              onClick={() => navigate(`/datasets/${kbId}/documents`)}
            >
              <FaExternalLinkAlt className="datasetSettingsApiIcon" />
              ドキュメントに移動
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DatasetSettings;
