import React, { useState } from 'react';
import { FiSearch, FiClock, FiCode, FiPlus, FiInfo, FiExternalLink, FiX, FiHelpCircle } from 'react-icons/fi';
import { FaHeadphones, FaSpider, FaRobot } from 'react-icons/fa';
import { BsImage, BsLink45Deg } from 'react-icons/bs';
import { IoFilterOutline } from 'react-icons/io5';
import { MdOutlineWatchLater, MdClose } from 'react-icons/md';
import './css/Tools.css';

// 创建工具弹出框组件
const CreateToolModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onOpenAuthModal: () => void;
}> = ({ isOpen, onClose, onOpenAuthModal }) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-container">
        <div className="modal-header">
          <h2>カスタムツールを作成する</h2>
          <button className="modal-close-button" onClick={onClose}>
            <MdClose />
          </button>
        </div>
        <div className="modal-content">
          <div className="form-group">
            <label>名前 <span className="required">*</span></label>
            <div className="name-input-container">
              <div className="name-icon-container">
                <FaRobot className="name-icon" />
              </div>
              <input type="text" className="name-input" placeholder="ツール名を入力してください" />
            </div>
          </div>

          <div className="form-group">
            <label>スキーマ <span className="required">*</span></label>
            <div className="schema-header">
              <span className="schema-link">OpenAPI-Swagger仕様を参照する</span>
              <BsLink45Deg />
            </div>
            <textarea placeholder="ここにOpenAPIスキーマを入力してください"></textarea>
          </div>

          <div className="form-group">
            <label>利用可能なツール</label>
            <div className="tool-table">
              <div className="tool-table-header">
                <div className="tool-table-cell">名前</div>
                <div className="tool-table-cell">説明</div>
                <div className="tool-table-cell">メソッド</div>
                <div className="tool-table-cell">パス</div>
                <div className="tool-table-cell">アクション</div>
              </div>
            </div>
          </div>

          <div className="form-group">
            <label>認証方法</label>
            <div className="auth-method-selector" onClick={onOpenAuthModal}>
              <span>なし</span>
              <FiHelpCircle className="help-icon" />
            </div>
          </div>

          <div className="form-group">
            <label>ラベル</label>
            <div className="label-selector">
              <span>ラベルを選択してください（オプション）</span>
              <span className="dropdown-arrow">▼</span>
            </div>
          </div>

          <div className="modal-footer">
            <button className="cancel-button" onClick={onClose}>キャンセル</button>
            <button className="save-button">保存</button>
          </div>
        </div>
      </div>
    </div>
  );
};

// 认证方法弹出框组件
const AuthMethodModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
}> = ({ isOpen, onClose }) => {
  const [authType, setAuthType] = useState('none');
  const [authMethod, setAuthMethod] = useState('basic');

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-container auth-modal">
        <div className="modal-header">
          <h2>認証方法</h2>
          <button className="modal-close-button" onClick={onClose}>
            <MdClose />
          </button>
        </div>
        <div className="modal-content">
          <div className="form-group">
            <label>認証タイプ</label>
            <div className="auth-type-options">
              <label className="auth-option">
                <input
                  type="radio"
                  name="authType"
                  value="none"
                  checked={authType === 'none'}
                  onChange={() => setAuthType('none')}
                  className="radio-input"
                />
                <span className="radio-custom"></span>
                <span className="auth-option-text">なし</span>
              </label>
              <label className="auth-option">
                <input
                  type="radio"
                  name="authType"
                  value="apiKey"
                  checked={authType === 'apiKey'}
                  onChange={() => setAuthType('apiKey')}
                  className="radio-input"
                />
                <span className="radio-custom"></span>
                <span className="auth-option-text">APIキー</span>
              </label>
            </div>
          </div>

          {authType === 'apiKey' && (
            <>
              <div className="form-group">
                <label>認証タイプ</label>
                <div className="auth-method-options">
                  <label className="auth-option">
                    <input
                      type="radio"
                      name="authMethod"
                      value="basic"
                      checked={authMethod === 'basic'}
                      onChange={() => setAuthMethod('basic')}
                      className="radio-input"
                    />
                    <span className="radio-custom"></span>
                    <span className="auth-option-text">ベーシック</span>
                  </label>
                  <label className="auth-option">
                    <input
                      type="radio"
                      name="authMethod"
                      value="bearer"
                      checked={authMethod === 'bearer'}
                      onChange={() => setAuthMethod('bearer')}
                      className="radio-input"
                    />
                    <span className="radio-custom"></span>
                    <span className="auth-option-text">ベアラー</span>
                  </label>
                  <label className="auth-option">
                    <input
                      type="radio"
                      name="authMethod"
                      value="custom"
                      checked={authMethod === 'custom'}
                      onChange={() => setAuthMethod('custom')}
                      className="radio-input"
                    />
                    <span className="radio-custom"></span>
                    <span className="auth-option-text">カスタム</span>
                  </label>
                </div>
              </div>

              <div className="form-group">
                <label>キー <FiHelpCircle className="help-icon-small" /></label>
                <input type="text" value="Authorization" className="auth-input" />
              </div>

              <div className="form-group">
                <label>値</label>
                <input type="text" placeholder="APIキーを入力してください" className="auth-input" />
              </div>
            </>
          )}

          <div className="modal-footer">
            <button className="cancel-button" onClick={onClose}>キャンセル</button>
            <button className="save-button">保存</button>
          </div>
        </div>
      </div>
    </div>
  );
};

const Tools: React.FC = () => {
  const [activeTab, setActiveTab] = useState('tools');
  const [searchText, setSearchText] = useState('');
  const [showAllTools, setShowAllTools] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  // 工具数据
  const tools = [
    {
      id: 'code-interpreter',
      name: 'Code Interpreter',
      icon: <FiCode className="tool-icon code" />,
      description: 'Run a piece of code and get the result back.',
      tag: 'code',
      category: 'productivity'
    },
    {
      id: 'current-time',
      name: 'CurrentTime',
      icon: <MdOutlineWatchLater className="tool-icon time" />,
      description: 'A tool for getting the current time.',
      tag: 'time',
      category: 'utilities'
    },
    {
      id: 'audio',
      name: 'Audio',
      icon: <FaHeadphones className="tool-icon audio" />,
      description: 'A tool for tts and asr.',
      tag: 'audio',
      category: 'utilities'
    },
    {
      id: 'web-scraper',
      name: 'WebScraper',
      icon: <FaSpider className="tool-icon web" />,
      description: 'Web Scraper tool kit is used to scrape web',
      tag: 'webscraper',
      category: 'productivity'
    },
    {
      id: 'getimg-ai',
      name: 'getimg.ai',
      icon: <BsImage className="tool-icon image" />,
      description: 'Getimg API integration for image generation and scraping.',
      tag: 'langgenius / getimgai',
      category: 'image'
    }
  ];

  // 过滤工具
  const filteredTools = tools.filter(tool =>
    (tool.name.toLowerCase().includes(searchText.toLowerCase()) ||
     tool.description.toLowerCase().includes(searchText.toLowerCase()) ||
     tool.tag.toLowerCase().includes(searchText.toLowerCase())) &&
    (showAllTools || tool.category === 'productivity')
  );

  return (
    <div className="tools-container">
      {/* 顶部导航栏 */}
      <div className="tools-tabs">
        <button
          className={`tools-tab ${activeTab === 'tools' ? 'active' : ''}`}
          onClick={() => setActiveTab('tools')}
        >
          ツール
        </button>
      </div>

      {/* 搜索和筛选 */}
      <div className="tools-search-bar">
        <div className="tools-filter">
          <button className="tools-filter-button">
            <IoFilterOutline style={{ marginRight: '5px' }} />
            すべてのタグ <span className="dropdown-arrow">▼</span>
          </button>
        </div>
        <div className="tools-search">
          <FiSearch className="search-icon" />
          <input
            type="text"
            placeholder="検索"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className="search-input"
          />
        </div>
      </div>

      {/* 工具列表 */}
      {activeTab === 'tools' && (
        <div className="tools-grid">

      {/* 新建 */}
        <div
          className="tool-card custom-tool-card"
          onClick={() => setIsCreateModalOpen(true)}
        >
          <div className="custom-tool-content">
            <FiPlus className="custom-tool-icon" />
            <p className="custom-tool-text">ツールを作成する</p>
          </div>
        </div>

          {filteredTools.map(tool => (
            <div key={tool.id} className="tool-card">
              <div className="tool-header">
                {tool.icon}
                <h3 className="tool-name">{tool.name}</h3>
              </div>
              <div className="tool-tag">{tool.tag}</div>
              <p className="tool-description">{tool.description}</p>
              <div className="tool-category">
                # {tool.category}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 弹出框 */}
      <CreateToolModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onOpenAuthModal={() => {
          setIsAuthModalOpen(true);
        }}
      />

      <AuthMethodModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />
    </div>
  );
};

export default Tools;
