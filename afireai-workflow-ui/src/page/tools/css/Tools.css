.tools-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 100vh;
}


/* 顶部导航栏 */

.tools-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
}

.tools-tab {
    padding: 10px 20px;
    background: transparent;
    border: none;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    position: relative;
}

.tools-tab.active {
    color: #4a90e2;
    font-weight: 600;
}

.tools-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #4a90e2;
}


/* 搜索和筛选 */

.tools-search-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.tools-filter {
    display: flex;
    align-items: center;
}

.tools-filter-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
}

.dropdown-arrow {
    font-size: 10px;
    color: #999;
    margin-left: 5px;
}

.tools-search {
    position: relative;
    width: 300px;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-input {
    width: 100%;
    padding: 8px 12px 8px 35px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 13px;
    background-color: #f5f5f5;
}

.search-input:focus {
    outline: none;
    border-color: #4a90e2;
    background-color: white;
}


/* 工具网格 */

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}


/* 工具卡片 */

.tool-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    height: 180px;
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
}

.tool-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tool-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.tool-icon {
    width: 36px;
    height: 36px;
    margin-right: 10px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.tool-icon.code {
    background-color: #e3f2fd;
    color: #2196f3;
}

.tool-icon.time {
    background-color: #fff3e0;
    color: #ff9800;
}

.tool-icon.audio {
    background-color: #f8bbd0;
    color: #e91e63;
}

.tool-icon.web {
    background-color: #e8f5e9;
    color: #4caf50;
}

.tool-icon.image {
    background-color: #e0f7fa;
    color: #00bcd4;
}

.tool-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.tool-tag {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.tool-description {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
    flex-grow: 1;
    margin-bottom: 10px;
}

.tool-category {
    font-size: 12px;
    color: #999;
}


/* 自定义工具卡片 */

.custom-tool-card {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 1px dashed #e0e0e0;
    background-color: white;
}

.custom-tool-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.custom-tool-icon {
    font-size: 24px;
    color: #4a90e2;
    margin-bottom: 10px;
}

.custom-tool-text {
    font-size: 14px;
    color: #333;
}


/* 自定义工具信息卡片 */

.custom-info-card {
    background-color: #f5f7fa;
    border: none;
    box-shadow: none;
    height: auto;
}

.custom-info-content {
    display: flex;
    align-items: center;
}

.custom-info-icon {
    font-size: 16px;
    color: #999;
    margin-right: 10px;
}

.custom-info-text {
    font-size: 14px;
    color: #666;
    flex-grow: 1;
}

.custom-info-link {
    font-size: 16px;
    color: #4a90e2;
    cursor: pointer;
}


/* 空状态 */

.tools-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    color: #999;
    font-size: 14px;
}


/* 弹出框样式 */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-container {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h2 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.modal-close-button {
    background: transparent;
    border: none;
    font-size: 20px;
    color: #999;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.form-group input[type="text"],
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    background-color: #f5f5f5;
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.input-with-icon {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.robot-icon {
    color: #4a90e2;
    font-size: 18px;
}

.input-with-icon input {
    padding-left: 40px;
}


/* 名前输入框样式 */

.name-input-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.name-icon-container {
    width: 48px;
    height: 48px;
    background-color: #fff8e1;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.name-icon {
    color: #ffa000;
    font-size: 24px;
}

.name-input {
    flex: 1;
    height: 48px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 4px;
    padding: 0 15px;
    font-size: 14px;
}

.schema-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
}

.schema-link {
    color: #4a90e2;
    margin-right: 5px;
    cursor: pointer;
}

.tool-table {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.tool-table-header {
    display: flex;
    background-color: #f5f5f5;
    padding: 10px;
    font-size: 13px;
    font-weight: 500;
    color: #666;
}

.tool-table-cell {
    flex: 1;
    padding: 0 5px;
}

.auth-method-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f5f5f5;
    cursor: pointer;
}

.help-icon {
    color: #999;
    font-size: 16px;
}

.help-icon-small {
    color: #999;
    font-size: 14px;
    margin-left: 5px;
}

.label-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f5f5f5;
    color: #999;
    cursor: pointer;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.cancel-button {
    padding: 8px 16px;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.save-button {
    padding: 8px 16px;
    background-color: #1a73e8;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    color: white;
    cursor: pointer;
}


/* 认证方法弹出框样式 */

.auth-modal {
    max-width: 500px;
}

.auth-type-options,
.auth-method-options {
    display: flex;
    gap: 30px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.auth-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
    min-height: 24px;
}


/* 自定义单选按钮样式 */

.auth-option {
    position: relative;
    padding-left: 30px;
}

.radio-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.radio-custom {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border: 2px solid #ddd;
    border-radius: 50%;
    box-sizing: border-box;
}

.auth-option:hover .radio-custom {
    border-color: #ccc;
}

.radio-input:checked~.radio-custom {
    border-color: #1a73e8;
}

.radio-custom:after {
    content: "";
    position: absolute;
    display: none;
}

.radio-input:checked~.radio-custom:after {
    display: block;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #1a73e8;
}

.auth-option-text {
    font-size: 14px;
    color: #333;
    margin-left: 10px;
    line-height: 20px;
}

.auth-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    background-color: #f5f5f5;
}

.required {
    color: #e53935;
    margin-left: 2px;
}