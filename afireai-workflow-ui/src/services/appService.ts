// 应用服务API

// 创建应用的接口
export interface CreateAppRequest {
  app_name: string;
  app_description: string;
  tag: string;
  flow_json: string;
}

// 应用响应接口
export interface AppResponse {
  app_name: string;
  app_description: string;
  tag: string;
  flow_json: string;
  user_id: string;
  ip_addr: string;
  app_id: string;
  created_at: string;
  updated_at: string;
}

// 应用卡片接口
export interface AppCard {
  id?: string;
  title: string;
  desc: string;
  tag: string;
  icon?: string;
  isCreateCard?: boolean;
}

// API基础URL - 使用Vite代理
const API_BASE_URL = '/api';

// 从认证服务获取令牌
import { getAccessToken } from './authService';

// 获取认证令牌
const getAuthToken = (): string | null => {
  return getAccessToken();
};

// 创建应用
export const createApp = async (appName: string, appDescription: string, appIconFile: string, tag: string = 'workflow', flowJson: string = ''): Promise<AppResponse> => {
  try {
    console.log('Creating app with data:', { appName, appDescription, appIconFile, tag, flowJson });

    const requestData = {
      app_name: appName,
      app_description: appDescription,
      tag: tag,
      flow_json: flowJson
    };

    console.log('Request payload:', JSON.stringify(requestData));

    const response = await fetch(`${API_BASE_URL}/apps/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {}),
        'Accept': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      },
      credentials: 'include',
      mode: 'cors',
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'No error details');
      console.error(`API error: ${response.status}`, errorText);
      throw new Error(`API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('API response:', data);
    return data as AppResponse;
  } catch (error) {
    console.error('Error creating app:', error);
    throw error;
  }
};

// 获取应用列表
export const getApps = async (): Promise<AppResponse[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/apps/list`, {
      method: 'GET',
      headers: {
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json() as AppResponse[];
  } catch (error) {
    console.error('Error fetching apps:', error);
    throw error;
  }
};

// 获取应用详情
export const getAppById = async (appId: string): Promise<AppResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/apps/${appId}`, {
      method: 'GET',
      headers: {
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json() as AppResponse;
  } catch (error) {
    console.error(`Error fetching app ${appId}:`, error);
    throw error;
  }
};

// 更新应用
export const updateApp = async (appId: string, appData: Partial<CreateAppRequest>): Promise<AppResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/apps/${appId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
      },
      body: JSON.stringify(appData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API error: ${response.status} - ${errorText}`);
    }

    return await response.json() as AppResponse;
  } catch (error) {
    console.error(`Error updating app ${appId}:`, error);
    throw error;
  }
};

// 获取应用图标
export const getAppIcon = async (appId: string): Promise<string | null> => {
  try {
    const response = await fetch(`${API_BASE_URL}/apps/${appId}/icon/raw`, {
      method: 'GET',
      headers: {
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null; // 图标不存在
      }
      throw new Error(`API error: ${response.status}`);
    }

    // 将blob转换为ObjectURL
    const blob = await response.blob();
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error(`Error fetching icon for app ${appId}:`, error);
    return null;
  }
};

// 更新应用图标
export const updateAppIcon = async (
  appId: string,
  file: File,
  name?: string,
  description?: string
) => {
  const formData = new FormData();
  formData.append('file', file);
  if (name) formData.append('name', name);
  if (description) formData.append('description', description);

  // 修改请求路径，移除多余的/v1前缀
  const response = await fetch(`${API_BASE_URL}/apps/${appId}/icon`, {
    method: "PUT",
    headers: {
      ...(getAuthToken() ? { Authorization: `Bearer ${getAuthToken()}` } : {}),
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`Icon update failed: ${response.status}`);
  }
  return await response.json();
};

// 删除应用
export const deleteApp = async (appId: string): Promise<void> => {
  try {
    const response = await fetch(`${API_BASE_URL}/apps/${appId}`, {
      method: 'DELETE',
      headers: {
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
  } catch (error) {
    console.error(`Error deleting app ${appId}:`, error);
    throw error;
  }
};

// 工作流运行响应类型
export interface WorkflowRunResponse {
  workflow_id: string;
  workflow_run_id: string;
  status: string;
}

// 创建工作流运行请求类型
export interface CreateWorkflowRunRequest {
  app_id: string;
  type: string;
  status: string;
  params: string;
  memory: string;
  flow_json: string;
}

// 创建工作流运行响应类型
export interface CreateWorkflowRunResponse {
  app_id: string;
  workflow_run_id: string;
}

// 节点状态类型
export interface NodeStatus {
  node_id: string;
  status: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED';
  started_at?: string;
  finished_at?: string;
  output_type?: string;
  output?: string;
}

// 工作流状态响应类型
export interface WorkflowStatusResponse {
  workflow_id: string;
  workflow_run_id: string;
  status: string;
  started_at: string;
  finished_at?: string;
  nodes: {
    total: number;
    completed: number;
    running: number;
    failed: number;
    pending: number;
  };
  node_details: NodeStatus[];
}

// 创建工作流运行记录
export const createWorkflowRun = async (request: CreateWorkflowRunRequest): Promise<CreateWorkflowRunResponse> => {
  try {
    console.log('Creating workflow run with data:', request);

    const response = await fetch(`${API_BASE_URL}/workflow-runs/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {}),
        'Accept': 'application/json'
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'No error details');
      console.error(`API error: ${response.status}`, errorText);
      throw new Error(`API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Create workflow run response:', data);
    return data as CreateWorkflowRunResponse;
  } catch (error) {
    console.error('Error creating workflow run:', error);
    throw error;
  }
};

// 运行工作流
export const runWorkflow = async (appId: string): Promise<WorkflowRunResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/run/${appId}/workflow`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error running workflow for app ${appId}:`, error);
    throw error;
  }
};

// 获取工作流状态
export const getWorkflowStatus = async (workflowId: string, workflowRunId: string): Promise<WorkflowStatusResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/run/${workflowId}/status/${workflowRunId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error getting workflow status for ${workflowId}/${workflowRunId}:`, error);
    throw error;
  }
};
