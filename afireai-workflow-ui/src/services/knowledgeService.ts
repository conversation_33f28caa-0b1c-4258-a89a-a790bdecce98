import { getAccessToken } from './authService';

const API_BASE_URL = '/api';

const getAuthToken = (): string | null => {
  return getAccessToken();
};

export interface CreateKnowledgeRequest {
  kb_name: string;
  kb_description?: string;
}

export interface UpdateKnowledgeRequest {
  kb_name?: string;
  kb_description?: string;
}

export interface KnowledgeResponse {
  kb_id: string;
  kb_name: string;
  kb_description: string;
  embedding_model: string;
  chunk_size: number;
  chunk_overlap: number;
  chunk_identifier: string;
  top_k: number;
  score_val: number;
  ip_addr: string;
  created_at: string;
  updated_at: string;
}

export interface KnowledgeFile {
  file_id: string;
  kb_id: string;
  user_id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  storage_path: string;
  status: string;
  chunk_identifier?: string;
  uploaded_by?: string;
  ip_addr?: string;
  created_at: string;
  updated_at: string;
}

export interface KnowledgeProcessRequest {
  embedding_model: string;
  chunk_identifier: string;
  chunk_size: number;
  chunk_overlap: number;
  remove_line_breaks?: boolean;
  remove_urls?: boolean;
}

export interface KnowledgeProcessResponse {
  success: boolean;
  message: string;
  processed_files: number;
  total_chunks: number;
}

export const createKnowledge = async (payload: CreateKnowledgeRequest): Promise<KnowledgeResponse> => {
  const safePayload = {
    ...payload,
    kb_description: payload.kb_description ?? ""
  };
  const response = await fetch(`${API_BASE_URL}/knowledge/create`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
    },
    body: JSON.stringify(safePayload)
  });
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API error: ${response.status} - ${errorText}`);
  }
  return await response.json() as KnowledgeResponse;
};

export const uploadKnowledgeFiles = async (kbId: string, files: File[]): Promise<KnowledgeFile[]> => {
  const formData = new FormData();
  files.forEach(file => formData.append('files', file));
  const response = await fetch(`${API_BASE_URL}/knowledge-files/${kbId}/files`, {
    method: 'POST',
    headers: {
      ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
    },
    body: formData
  });
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API error: ${response.status} - ${errorText}`);
  }
  return await response.json() as KnowledgeFile[];
};

export const listKnowledgeFiles = async (kbId: string): Promise<KnowledgeFile[]> => {
  const response = await fetch(`${API_BASE_URL}/knowledge-files/${kbId}/files`, {
    method: 'GET',
    headers: {
      ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
    }
  });
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API error: ${response.status} - ${errorText}`);
  }
  return await response.json() as KnowledgeFile[];
};

export const deleteKnowledgeFile = async (fileId: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/knowledge-files/${fileId}`, {
    method: 'DELETE',
    headers: {
      ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
    }
  });
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API error: ${response.status} - ${errorText}`);
  }
};

export const listKnowledgeBases = async (): Promise<KnowledgeResponse[]> => {
  const response = await fetch(`${API_BASE_URL}/knowledge`, {
    method: 'GET',
    headers: {
      ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
    }
  });
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API error: ${response.status} - ${errorText}`);
  }
  return await response.json() as KnowledgeResponse[];
};

export const deleteKnowledgeBase = async (kbId: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/knowledge/${kbId}`, {
    method: 'DELETE',
    headers: {
      ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
    }
  });
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API error: ${response.status} - ${errorText}`);
  }
};

export const updateKnowledgeBase = async (kbId: string, payload: UpdateKnowledgeRequest): Promise<KnowledgeResponse> => {
  const response = await fetch(`${API_BASE_URL}/knowledge/${kbId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
    },
    body: JSON.stringify(payload)
  });
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API error: ${response.status} - ${errorText}`);
  }
  return await response.json() as KnowledgeResponse;
};

export const processKnowledge = async (kbId: string, payload: KnowledgeProcessRequest): Promise<KnowledgeProcessResponse> => {
  const response = await fetch(`${API_BASE_URL}/knowledge/add/${kbId}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
    },
    body: JSON.stringify(payload)
  });
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API error: ${response.status} - ${errorText}`);
  }
  return await response.json() as KnowledgeProcessResponse;
};