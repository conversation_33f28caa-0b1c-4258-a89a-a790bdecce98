// src/services/logsService.ts

import { getAccessToken } from './authService';

// API基础URL
const API_BASE_URL = '/api';

// 获取认证令牌
const getAuthToken = (): string | null => {
  return getAccessToken();
};

export interface LogEntry {
  user_id: string;
  app_id: string;
  workflow_run_id: string;
  type: string;
  status: 'SUCCESS' | 'FAILED' | 'RUNNING' | null;
  params: string | null;
  memory: string | null;
  flow_json: string;
  started_at: string;
  finished_at: string | null;
  ip_addr: string;
  created_at: string;
  updated_at: string;
}

export interface WorkflowRunNodeLogEntry {
  id: string;
  user_id: string;
  workflow_run_id: string;
  node_id: string;
  node_ver: string;
  status: 'SUCCESS' | 'FAILED' | 'RUNNING' | null;
  params: string | null;
  memory: string | null;
  flow_json: string;
  output_type: string;
  output: string;
  started_at: string;
  finished_at: string | null;
  ip_addr: string;
  created_at: string;
  updated_at: string;
}

export interface WorkflowRunNodeLogsResponse {
  nodes: WorkflowRunNodeLogEntry[];
  // Pagination can be added if needed in the future
}

export interface LogsResponse {
  logs: LogEntry[];
  pagination: {
    page: number;
    page_size: number;
    total_count: number;
    total_pages: number;
  };
}

export interface LogsFilters {
  app_id?: string;
  status?: string;
  time_range?: string;
  search?: string;
  page?: number;
  page_size?: number;
}

/**
 * ワークフローログを取得する
 */
export const getWorkflowLogs = async (filters: LogsFilters = {}): Promise<LogsResponse> => {
  const params = new URLSearchParams();

  if (filters.status && filters.status !== 'ALL') {
    params.append('status', filters.status);
  }

  if (filters.time_range && filters.time_range !== 'all') {
    params.append('time_range', filters.time_range);
  }

  if (filters.search) {
    params.append('search', filters.search);
  }

  if (filters.page) {
    params.append('page', filters.page.toString());
  }

  if (filters.page_size) {
    params.append('page_size', filters.page_size.toString());
  }

  // 构建URL - 如果有app_id则使用app特定的端点
  let url: string;
  if (filters.app_id) {
    url = `${API_BASE_URL}/workflow-runs/app/${filters.app_id}/logs${params.toString() ? '?' + params.toString() : ''}`;
  } else {
    url = `${API_BASE_URL}/workflow-runs/logs${params.toString() ? '?' + params.toString() : ''}`;
  }

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {}),
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'No error details');
      console.error(`API error: ${response.status}`, errorText);
      throw new Error(`API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Workflow logs response:', data);
    return data as LogsResponse;
  } catch (error) {
    console.error('Failed to fetch workflow logs:', error);
    throw error;
  }
};

/**
 * 指定されたワークフロー実行IDに関連するワークフロー実行ノードログを取得する
 */
export const getWorkflowRunNodeLogs = async (workflowRunId: string): Promise<WorkflowRunNodeLogEntry[]> => {
  const url = `${API_BASE_URL}/workflow-run-nodes/workflow-run/${workflowRunId}`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {}),
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'No error details');
      console.error(`API error: ${response.status}`, errorText);
      throw new Error(`API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Workflow run node logs response:', data);
    // The API returns a list directly, not an object with a 'nodes' key
    return data as WorkflowRunNodeLogEntry[];
  } catch (error) {
    console.error('Failed to fetch workflow run node logs:', error);
    throw error;
  }
};

/**
 * ランタイムを計算する（started_atとfinished_atから）
 */
export const calculateRuntime = (startedAt: string, finishedAt: string | null): string => {
  if (!finishedAt) {
    return '-';
  }

  const start = new Date(startedAt);
  const end = new Date(finishedAt);
  const diffMs = end.getTime() - start.getTime();

  if (diffMs < 1000) {
    return `${diffMs}ms`;
  } else {
    return `${(diffMs / 1000).toFixed(3)}s`;
  }
};

/**
 * 日時をフォーマットする
 */
export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}/${month}/${day} ${hours}:${minutes}`;
};

/**
 * ステータスの表示名を取得する
 */
export const getStatusDisplayName = (status: string | null): string => {
  switch (status) {
    case 'SUCCESS':
      return 'SUCCESS';
    case 'FAILED':
      return 'FAILED';
    case 'RUNNING':
      return 'RUNNING';
    default:
      return 'UNKNOWN';
  }
};

/**
 * ステータスのCSSクラス名を取得する
 */
export const getStatusClassName = (status: string | null): string => {
  switch (status) {
    case 'SUCCESS':
      return 'success';
    case 'FAILED':
      return 'failed';
    case 'RUNNING':
      return 'running';
    default:
      return 'unknown';
  }
};

/**
 * 時間範囲の表示名を取得する
 */
export const getTimeRangeDisplayName = (timeRange: string): string => {
  switch (timeRange) {
    case 'today':
      return '今日';
    case '7days':
      return '過去7日間';
    case '1month':
      return '過去1ヶ月';
    case '6months':
      return '過去6ヶ月';
    case 'all':
      return 'すべての期間';
    default:
      return '過去7日間';
  }
};

/**
 * ステータスの表示名を取得する（日本語）
 */
export const getStatusDisplayNameJa = (status: string): string => {
  switch (status) {
    case 'ALL':
      return 'すべて';
    case 'SUCCESS':
      return '成功';
    case 'FAILED':
      return '失敗';
    case 'RUNNING':
      return '実行中';
    default:
      return 'すべて';
  }
};
