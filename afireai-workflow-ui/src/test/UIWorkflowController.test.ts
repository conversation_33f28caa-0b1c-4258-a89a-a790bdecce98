/**
 * UI侧工作流控制器测试
 * 验证新的架构是否正常工作
 */

import { UIWorkflowController, NodeExecutionStatus } from '../utils/UIWorkflowController';
import { Node, Edge } from '@xyflow/react';

// 模拟测试数据
const mockNodes: Node[] = [
  {
    id: 'start-1',
    type: 'startNode',
    position: { x: 100, y: 100 },
    data: { label: '开始' }
  },
  {
    id: 'llm-1',
    type: 'llmNode',
    position: { x: 300, y: 100 },
    data: { label: 'LLM处理' }
  },
  {
    id: 'end-1',
    type: 'endNode',
    position: { x: 500, y: 100 },
    data: { label: '结束' }
  }
];

const mockEdges: Edge[] = [
  {
    id: 'edge-1',
    source: 'start-1',
    target: 'llm-1'
  },
  {
    id: 'edge-2',
    source: 'llm-1',
    target: 'end-1'
  }
];

// 模拟API响应
global.fetch = jest.fn();

describe('UIWorkflowController', () => {
  let controller: UIWorkflowController;
  let nodeStateChanges: Array<{ nodeId: string, status: NodeExecutionStatus }>;
  let workflowCompleted: boolean;

  beforeEach(() => {
    nodeStateChanges = [];
    workflowCompleted = false;

    controller = new UIWorkflowController({
      apiBaseUrl: 'http://localhost:8000',
      onNodeStateChange: (nodeId, state) => {
        nodeStateChanges.push({ nodeId, status: state.status });
      },
      onWorkflowComplete: (success, results) => {
        workflowCompleted = true;
      }
    });

    // 重置fetch mock
    (fetch as jest.Mock).mockClear();
  });

  test('应该正确构建依赖关系', () => {
    // 这个测试需要访问私有方法，所以我们通过执行工作流来间接测试
    expect(controller).toBeDefined();
  });

  test('应该按正确顺序执行节点', async () => {
    // 模拟API响应
    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          app_id: 'test-app',
          workflow_run_id: 'test-workflow-run'
        })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, message: 'Start node completed' })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, message: 'LLM node completed' })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, message: 'End node completed' })
      });

    const flow_json = JSON.stringify({ nodes: mockNodes, edges: mockEdges });
    
    await controller.executeWorkflow(mockNodes, mockEdges, 'test-app-id', flow_json);

    // 验证节点状态变化顺序
    expect(nodeStateChanges.length).toBeGreaterThan(0);
    
    // 验证所有节点都初始化为PENDING
    const pendingStates = nodeStateChanges.filter(change => change.status === 'PENDING');
    expect(pendingStates.length).toBe(3); // 3个节点都应该初始化为PENDING

    // 验证工作流完成
    expect(workflowCompleted).toBe(true);
  });

  test('应该正确处理API错误', async () => {
    // 模拟API错误
    (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    const flow_json = JSON.stringify({ nodes: mockNodes, edges: mockEdges });
    
    await controller.executeWorkflow(mockNodes, mockEdges, 'test-app-id', flow_json);

    // 验证错误处理
    expect(workflowCompleted).toBe(true); // 应该调用完成回调，即使失败
  });

  test('应该正确加载节点映射', async () => {
    // 模拟nodeMap.json响应
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        endpoint_map: {
          'startNode': 'run/v1/startNode',
          'llmNode': 'run/v1/llmNode',
          'endNode': 'run/v1/endNode'
        }
      })
    });

    // 创建新的控制器实例来触发加载
    const newController = new UIWorkflowController({
      apiBaseUrl: 'http://localhost:8000'
    });

    // 等待一下让异步加载完成
    await new Promise(resolve => setTimeout(resolve, 100));

    expect(fetch).toHaveBeenCalledWith('/nodeMap.json');
  });

  test('应该正确保存到localStorage', async () => {
    // 模拟localStorage
    const localStorageMock = {
      setItem: jest.fn(),
      getItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn()
    };
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock
    });

    // 模拟创建工作流运行的API响应
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        app_id: 'test-app-123',
        workflow_run_id: 'test-workflow-run-456'
      })
    });

    const flow_json = JSON.stringify({ nodes: mockNodes, edges: mockEdges });
    
    try {
      await controller.executeWorkflow(mockNodes, mockEdges, 'test-app-id', flow_json);
    } catch (error) {
      // 忽略后续的API调用错误，我们只关心localStorage
    }

    // 验证localStorage调用
    expect(localStorageMock.setItem).toHaveBeenCalledWith('current_app_id', 'test-app-123');
    expect(localStorageMock.setItem).toHaveBeenCalledWith('current_workflow_run_id', 'test-workflow-run-456');
  });
});

// 集成测试示例
describe('UIWorkflowController Integration', () => {
  test('完整的工作流执行流程', async () => {
    const executionLog: string[] = [];
    
    const controller = new UIWorkflowController({
      apiBaseUrl: 'http://localhost:8000',
      onNodeStateChange: (nodeId, state) => {
        executionLog.push(`${nodeId}: ${state.status}`);
      },
      onWorkflowComplete: (success, results) => {
        executionLog.push(`Workflow completed: ${success ? 'SUCCESS' : 'FAILED'}`);
      }
    });

    // 模拟完整的API调用序列
    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ app_id: 'app-1', workflow_run_id: 'run-1' })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      });

    const flow_json = JSON.stringify({ nodes: mockNodes, edges: mockEdges });
    await controller.executeWorkflow(mockNodes, mockEdges, 'test-app', flow_json);

    // 验证执行日志
    expect(executionLog).toContain('start-1: PENDING');
    expect(executionLog).toContain('llm-1: PENDING');
    expect(executionLog).toContain('end-1: PENDING');
    expect(executionLog).toContain('Workflow completed: SUCCESS');
  });
});
