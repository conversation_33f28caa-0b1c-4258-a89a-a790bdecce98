import React, { createContext, useContext, ReactNode } from 'react';

interface NodeExecutionContextType {
  executeSingleNode: (nodeId: string) => Promise<void>;
}

const NodeExecutionContext = createContext<NodeExecutionContextType | null>(null);

interface NodeExecutionProviderProps {
  children: ReactNode;
  executeSingleNode: (nodeId: string) => Promise<void>;
}

export const NodeExecutionProvider: React.FC<NodeExecutionProviderProps> = ({
  children,
  executeSingleNode
}) => {
  return (
    <NodeExecutionContext.Provider value={{ executeSingleNode }}>
      {children}
    </NodeExecutionContext.Provider>
  );
};

export const useNodeExecution = () => {
  const context = useContext(NodeExecutionContext);
  if (!context) {
    throw new Error('useNodeExecution must be used within a NodeExecutionProvider');
  }
  return context;
};
