/**
 * UI侧工作流控制器 - 完全替代API侧orchestrator
 * 直接调用nodeMap.json中定义的各节点API
 *
 * 设计思路：
 * 1. 移除API侧的orchestrator.py复杂编排逻辑
 * 2. UI前台完全控制工作流执行顺序和依赖关系
 * 3. 直接调用各节点API端点，简化架构
 */

import { Node, Edge } from '@xyflow/react';
import { createWorkflowRun, CreateWorkflowRunRequest } from '../services/appService';
import { getAccessToken } from '../services/authService';

// 节点执行状态
export type NodeExecutionStatus = 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'WAITING_MANUAL';

// 节点执行状态接口
export interface NodeExecutionState {
  nodeId: string;
  status: NodeExecutionStatus;
  startTime?: string;
  endTime?: string;
  result?: any;
  error?: string;
}

// 依赖关系映射
export interface DependencyMap {
  parents: Map<string, Set<string>>;  // nodeId -> 前置节点IDs
  children: Map<string, Set<string>>; // nodeId -> 后续节点IDs
}

// 工作流控制器配置
export interface WorkflowControllerConfig {
  apiBaseUrl: string;
  onNodeStateChange?: (nodeId: string, state: NodeExecutionState) => void;
  onWorkflowComplete?: (success: boolean, results: Map<string, NodeExecutionState>) => void;
}

export class UIWorkflowController {
  private config: WorkflowControllerConfig;
  private dependencies: DependencyMap = { parents: new Map(), children: new Map() };
  private nodeStates = new Map<string, NodeExecutionState>();
  private completedNodes = new Set<string>();
  private failedNodes = new Set<string>();
  private nodeMapping: Record<string, string> = {}; // nodeType -> API endpoint
  private nodes: Node[] = [];
  private isExecuting = false;
  private workflowCompleted = false; // 添加标志防止重复触发完成回调

  constructor(config: WorkflowControllerConfig) {
    this.config = config;
    this.loadNodeMapping();
  }

  /**
   * 加载节点映射配置
   */
  private async loadNodeMapping(): Promise<void> {
    try {
      const response = await fetch('/nodeMap.json');
      const config = await response.json();
      this.nodeMapping = config.endpoint_map || {};
      console.log('Loaded node mapping:', this.nodeMapping);
    } catch (error) {
      console.error('Failed to load node mapping:', error);
      // 使用默认映射
      this.nodeMapping = {
        'startNode': 'run/v1/startNode',
        'endNode': 'run/v1/endNode',
        'llmNode': 'run/v1/llmNode',
        'codeNode': 'run/v1/codeNode',
        'datasetGrepNode': 'run/v1/datasetGrepNode',
        'httpRequestNode': 'run/v1/httpRequestNode'
      };
    }
  }

  /**
   * 构建依赖关系（从orchestrator.py移植）
   */
  private buildDependencies(nodes: Node[], edges: Edge[]): DependencyMap {
    const parents = new Map<string, Set<string>>();
    const children = new Map<string, Set<string>>();

    // 初始化所有节点
    nodes.forEach(node => {
      parents.set(node.id, new Set());
      children.set(node.id, new Set());
    });

    // 构建依赖关系
    edges.forEach(edge => {
      const src = edge.source;
      const tgt = edge.target;
      if (src && tgt) {
        parents.get(tgt)?.add(src);
        children.get(src)?.add(tgt);
      }
    });

    console.log('Dependencies built:', {
      parents: Object.fromEntries(parents),
      children: Object.fromEntries(children)
    });

    return { parents, children };
  }

  /**
   * 初始化执行状态
   */
  private initializeExecution(nodes: Node[]): void {
    console.log('Initializing workflow execution...');

    this.nodeStates.clear();
    this.completedNodes.clear();
    this.failedNodes.clear();
    this.workflowCompleted = false; // 重置完成标志

    // 所有节点初始状态为PENDING
    nodes.forEach(node => {
      const state: NodeExecutionState = {
        nodeId: node.id,
        status: 'PENDING'
      };
      this.nodeStates.set(node.id, state);
      this.config.onNodeStateChange?.(node.id, state);
    });
  }

  /**
   * Step 1: 创建工作流运行记录 - 使用appService中的认证机制
   */
  private async createWorkflowRun(app_id: string, flow_json: string): Promise<{ app_id: string, workflow_run_id: string }> {
    try {
      const request: CreateWorkflowRunRequest = {
        type: "WEB",
        app_id: app_id,
        status: "RUNNING",
        params: "",
        memory: "",
        flow_json: flow_json
      };

      console.log('Creating workflow run via appService:', request);
      const result = await createWorkflowRun(request);

      // 保存到localStorage
      localStorage.setItem('current_app_id', result.app_id);
      localStorage.setItem('current_workflow_run_id', result.workflow_run_id);

      return result;
    } catch (error) {
      console.error('Failed to create workflow run:', error);
      throw error;
    }
  }

  /**
   * 主执行函数
   */
  public async executeWorkflow(nodes: Node[], edges: Edge[], app_id: string, flow_json: string): Promise<void> {
    if (this.isExecuting) {
      console.warn('Workflow is already executing');
      return;
    }

    this.isExecuting = true;
    this.nodes = nodes;

    try {
      console.log('Starting workflow execution...');

      // Step 1: 创建工作流运行记录
      const { workflow_run_id } = await this.createWorkflowRun(app_id, flow_json);
      console.log('Created workflow run:', workflow_run_id);

      // Step 2: 构建依赖关系和初始化状态
      this.dependencies = this.buildDependencies(nodes, edges);
      this.initializeExecution(nodes);

      // Step 3: 开始执行
      await this.startExecution(workflow_run_id);

    } catch (error) {
      console.error('Workflow execution failed:', error);
      this.config.onWorkflowComplete?.(false, this.nodeStates);
    } finally {
      this.isExecuting = false;
    }
  }

  /**
   * 开始执行 - 找到并执行startNode
   */
  private async startExecution(workflowRunId: string): Promise<void> {
    // 找到startNode
    const startNodes = this.nodes.filter(node => node.type === 'startNode');
    if (startNodes.length === 0) {
      throw new Error('No start node found');
    }

    console.log('Found start nodes:', startNodes.map(n => n.id));
    console.log('All nodes in workflow:', this.nodes.map(n => ({ id: n.id, type: n.type })));
    console.log('Dependencies:', {
      parents: Object.fromEntries(this.dependencies.parents),
      children: Object.fromEntries(this.dependencies.children)
    });

    // 执行所有startNode（通常只有一个）
    for (const startNode of startNodes) {
      await this.executeNode(startNode, workflowRunId);
    }

    // 强制检查是否有其他可以执行的节点（临时解决方案）
    await this.checkAndExecuteReadyNodes(workflowRunId);

    // 检查工作流是否完成
    this.checkWorkflowCompletion();
  }

  /**
   * 检查并执行所有准备就绪的节点（强制检查机制）
   */
  private async checkAndExecuteReadyNodes(workflowRunId: string): Promise<void> {
    console.log('Checking for ready nodes...');

    const readyNodes = this.nodes.filter(node => {
      // 跳过已处理的节点
      if (this.completedNodes.has(node.id) || this.failedNodes.has(node.id)) {
        return false;
      }

      // 跳过startNode（已经执行过）
      if (node.type === 'startNode') {
        return false;
      }

      // 检查依赖是否满足
      const parents = this.dependencies.parents.get(node.id) || new Set();

      // 修复：如果节点没有前置依赖（除了startNode），不要自动执行
      if (parents.size === 0) {
        console.log(`Node ${node.id} has no dependencies, skipping auto-execution`);
        return false;
      }

      const uncompletedParents = Array.from(parents).filter(
        parentId => !this.completedNodes.has(parentId)
      );

      return uncompletedParents.length === 0;
    });

    console.log('Ready nodes found:', readyNodes.map(n => ({ id: n.id, type: n.type })));

    if (readyNodes.length > 0) {
      // 并行执行所有准备就绪的节点
      const executionPromises = readyNodes.map(node => this.executeNode(node, workflowRunId));
      await Promise.allSettled(executionPromises);

      // 递归检查是否有更多节点可以执行
      await this.checkAndExecuteReadyNodes(workflowRunId);
    }
  }

  /**
   * 执行单个节点
   */
  private async executeNode(node: Node, workflowRunId: string): Promise<void> {
    const nodeId = node.id;

    console.log(`Attempting to execute node ${nodeId} (${node.type})`);

    // 检查是否已执行
    if (this.completedNodes.has(nodeId) || this.failedNodes.has(nodeId)) {
      console.log(`Node ${nodeId} already processed, skipping`);
      return;
    }

    // 检查前置依赖（除了startNode）
    if (node.type !== 'startNode') {
      const parents = this.dependencies.parents.get(nodeId) || new Set();
      const uncompletedParents = Array.from(parents).filter(
        parentId => !this.completedNodes.has(parentId)
      );

      if (uncompletedParents.length > 0) {
        console.log(`Node ${nodeId} waiting for dependencies: ${uncompletedParents}`);
        return; // 等待前置节点完成
      }
    }

    // 更新状态为RUNNING
    this.updateNodeState(nodeId, 'RUNNING');

    try {
      // 调用节点API
      console.log(`Calling API for node ${nodeId}`);
      const result = await this.callNodeAPI(node, workflowRunId);

      // 成功完成
      this.updateNodeState(nodeId, 'SUCCESS', result);
      this.completedNodes.add(nodeId);

      console.log(`Node ${nodeId} completed successfully`);

      // 执行后续节点
      await this.executeChildNodes(nodeId, workflowRunId);

    } catch (error) {
      // 执行失败
      console.error(`Node ${nodeId} execution failed:`, error);
      this.updateNodeState(nodeId, 'FAILED', null, error instanceof Error ? error.message : String(error));
      this.failedNodes.add(nodeId);
    }
  }

  /**
   * 执行后续节点
   */
  private async executeChildNodes(nodeId: string, workflowRunId: string): Promise<void> {
    const children = this.dependencies.children.get(nodeId) || new Set();

    if (children.size === 0) {
      console.log(`Node ${nodeId} has no children`);
      return;
    }

    console.log(`Executing children of ${nodeId}:`, Array.from(children));

    // 并行执行所有后续节点
    const executionPromises = Array.from(children).map(async (childId) => {
      const childNode = this.nodes.find(n => n.id === childId);
      if (childNode) {
        console.log(`Found child node ${childId} (${childNode.type}), executing...`);
        await this.executeNode(childNode, workflowRunId);
      } else {
        console.error(`Child node ${childId} not found in nodes array`);
      }
    });

    const results = await Promise.allSettled(executionPromises);
    console.log(`Execution results for children of ${nodeId}:`, results);

    // 检查工作流是否完成
    this.checkWorkflowCompletion();
  }

  /**
   * 调用节点API - 使用与appService相同的认证机制
   */
  private async callNodeAPI(node: Node, workflowRunId: string): Promise<any> {
    // 确保nodeMapping已加载
    if (Object.keys(this.nodeMapping).length === 0) {
      await this.loadNodeMapping();
    }

    const endpoint = this.nodeMapping[node.type as keyof typeof this.nodeMapping];
    if (!endpoint) {
      console.error('Available node mappings:', this.nodeMapping);
      throw new Error(`No endpoint mapping found for node type: ${node.type}`);
    }

    // 使用/api前缀，与appService保持一致
    const url = `/api/${endpoint}`;
    console.log(`Calling API: ${url}`);

    const authToken = getAccessToken();
    console.log('Using auth token:', authToken ? 'Token available' : 'No token');

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(authToken ? { 'Authorization': `Bearer ${authToken}` } : {}),
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        nodeId: node.id,
        workflowRunId: workflowRunId,
        ...node.data
      })
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'No error details');
      console.error(`API call failed: ${response.status} ${response.statusText}`, errorText);
      throw new Error(`API call failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return await response.json();
  }

  /**
   * 更新节点状态
   */
  private updateNodeState(nodeId: string, status: NodeExecutionStatus, result?: any, error?: string): void {
    const currentState = this.nodeStates.get(nodeId);
    if (currentState) {
      const newState: NodeExecutionState = {
        ...currentState,
        status,
        result,
        error,
        ...(status === 'RUNNING' && { startTime: new Date().toISOString() }),
        ...((status === 'SUCCESS' || status === 'FAILED') && { endTime: new Date().toISOString() })
      };

      this.nodeStates.set(nodeId, newState);

      // 通知UI更新
      this.config.onNodeStateChange?.(nodeId, newState);
    }
  }

  /**
   * 检查工作流是否完成
   */
  private checkWorkflowCompletion(): void {
    // 如果已经完成，不要重复触发
    if (this.workflowCompleted) {
      return;
    }

    const totalNodes = this.nodes.length;
    const processedNodes = this.completedNodes.size + this.failedNodes.size;

    if (processedNodes === totalNodes) {
      const success = this.failedNodes.size === 0;
      console.log('Workflow completed:', success ? 'SUCCESS' : 'FAILED');

      // 设置完成标志，防止重复触发
      this.workflowCompleted = true;

      // 触发完成回调
      this.config.onWorkflowComplete?.(success, this.nodeStates);
    }
  }

  /**
   * 停止执行
   */
  public stopExecution(): void {
    console.log('Stopping workflow execution...');
    this.isExecuting = false;
  }

  /**
   * 获取节点状态
   */
  public getNodeState(nodeId: string): NodeExecutionState | undefined {
    return this.nodeStates.get(nodeId);
  }

  /**
   * 获取所有节点状态
   */
  public getAllNodeStates(): Map<string, NodeExecutionState> {
    return new Map(this.nodeStates);
  }

  /**
   * 执行单个节点 - 用于节点执行按钮
   */
  public async executeSingleNode(nodeId: string, nodes: Node[], edges: Edge[], app_id: string, flow_json: string): Promise<void> {
    console.log(`Executing single node: ${nodeId}`);

    // 找到目标节点
    const targetNode = nodes.find(node => node.id === nodeId);
    if (!targetNode) {
      throw new Error(`Node ${nodeId} not found`);
    }

    // 如果是startNode或endNode，不允许单独执行
    if (targetNode.type === 'startNode' || targetNode.type === 'endNode') {
      throw new Error(`Cannot execute ${targetNode.type} individually`);
    }

    try {
      // 初始化依赖关系（如果还没有）
      if (this.dependencies.parents.size === 0) {
        this.dependencies = this.buildDependencies(nodes, edges);
        this.nodes = nodes;
      }

      // 检查前置依赖是否已完成
      const parents = this.dependencies.parents.get(nodeId) || new Set();

      // 对于单个节点执行，允许没有前置依赖的节点执行
      if (parents.size > 0) {
        const uncompletedParents = Array.from(parents).filter(
          parentId => !this.completedNodes.has(parentId)
        );

        if (uncompletedParents.length > 0) {
          throw new Error(`Node ${nodeId} has uncompleted dependencies: ${uncompletedParents.join(', ')}`);
        }
      }

      // 创建工作流运行记录（如果需要）
      let workflowRunId = localStorage.getItem('current_workflow_run_id');
      if (!workflowRunId) {
        const result = await this.createWorkflowRun(app_id, flow_json);
        workflowRunId = result.workflow_run_id;
      }

      // 初始化节点状态（如果还没有）
      if (!this.nodeStates.has(nodeId)) {
        const state: NodeExecutionState = {
          nodeId: nodeId,
          status: 'PENDING'
        };
        this.nodeStates.set(nodeId, state);
      }

      // 执行节点
      await this.executeNode(targetNode, workflowRunId);

      console.log(`Single node ${nodeId} execution completed`);

    } catch (error) {
      console.error(`Single node ${nodeId} execution failed:`, error);
      throw error;
    }
  }

  /**
   * 检查节点是否可以单独执行
   */
  public canExecuteSingleNode(nodeId: string, nodes: Node[], edges: Edge[]): { canExecute: boolean, reason?: string } {
    // 找到目标节点
    const targetNode = nodes.find(node => node.id === nodeId);
    if (!targetNode) {
      return { canExecute: false, reason: 'Node not found' };
    }

    // startNode和endNode不能单独执行
    if (targetNode.type === 'startNode' || targetNode.type === 'endNode') {
      return { canExecute: false, reason: `${targetNode.type} cannot be executed individually` };
    }

    // 构建依赖关系
    const dependencies = this.buildDependencies(nodes, edges);
    const parents = dependencies.parents.get(nodeId) || new Set();

    // 检查前置依赖是否已完成（只有当有前置依赖时才检查）
    if (parents.size > 0) {
      const uncompletedParents = Array.from(parents).filter(
        parentId => !this.completedNodes.has(parentId)
      );

      if (uncompletedParents.length > 0) {
        return {
          canExecute: false,
          reason: `Waiting for dependencies: ${uncompletedParents.join(', ')}`
        };
      }
    }

    // 检查节点是否已经执行过
    if (this.completedNodes.has(nodeId)) {
      return { canExecute: false, reason: 'Node already completed' };
    }

    if (this.failedNodes.has(nodeId)) {
      return { canExecute: false, reason: 'Node previously failed' };
    }

    return { canExecute: true };
  }

  /**
   * 重置状态
   */
  public resetState(): void {
    this.nodeStates.clear();
    this.completedNodes.clear();
    this.failedNodes.clear();
    this.isExecuting = false;
    this.workflowCompleted = false; // 重置完成标志
  }
}
