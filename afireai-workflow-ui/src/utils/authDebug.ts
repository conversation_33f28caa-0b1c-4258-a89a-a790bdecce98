/**
 * 认证调试工具
 * 用于检查认证状态和token
 */

import { getAccessToken, getAuthState } from '../services/authService';

export const debugAuth = () => {
  const authState = getAuthState();
  const accessToken = getAccessToken();
  
  console.log('=== 认证调试信息 ===');
  console.log('Auth State:', authState);
  console.log('Access Token:', accessToken);
  console.log('Is Logged In:', authState.isLoggedIn);
  console.log('Token Length:', accessToken?.length || 0);
  
  if (accessToken) {
    console.log('Token Preview:', accessToken.substring(0, 20) + '...');
  } else {
    console.log('❌ No access token found');
  }
  
  return {
    hasToken: !!accessToken,
    isLoggedIn: authState.isLoggedIn,
    token: accessToken
  };
};

export const testAPICall = async () => {
  const token = getAccessToken();
  
  if (!token) {
    console.error('❌ No token available for API test');
    return false;
  }
  
  try {
    console.log('🧪 Testing API call with token...');
    
    const response = await fetch('http://localhost:8000/apps', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('API Response Status:', response.status);
    
    if (response.ok) {
      console.log('✅ API call successful');
      return true;
    } else {
      console.error('❌ API call failed:', response.status, response.statusText);
      return false;
    }
  } catch (error) {
    console.error('❌ API call error:', error);
    return false;
  }
};
