/**
 * 文字列をクリップボードへコピーする共通関数
 * 戻り値: true  … 成功
 *         false … 失敗
 */
export async function copyText (text: string): Promise<boolean> {
  // ① モダンブラウザ
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch {
    // ② フォールバック（旧ブラウザ）
    try {
      const ta = document.createElement('textarea')
      ta.value = text
      ta.style.position = 'fixed'   // 画面に影響させない
      ta.style.opacity  = '0'
      document.body.appendChild(ta)
      ta.select()
      const ok = document.execCommand('copy')
      document.body.removeChild(ta)
      return ok
    } catch {
      return false
    }
  }
}
