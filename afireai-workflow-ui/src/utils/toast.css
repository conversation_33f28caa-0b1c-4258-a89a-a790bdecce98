.Toastify__toast-container {
    width: auto;
    min-width: 320px;
    max-width: 480px;
}

.Toastify__toast {
    border-radius: 12px;
    backdrop-filter: blur(10px);
    padding: 16px 20px;
    margin-bottom: 8px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    min-height: 56px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}


/* 隐藏进度条但保留功能 */

.Toastify__progress-bar {
    opacity: 0;
    height: 0;
    overflow: hidden;
}


/* 关闭按钮样式 */

.Toastify__close-button {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 20px;
    height: 20px;
    opacity: 0.6;
    transition: opacity 0.2s ease;
    color: inherit;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.Toastify__close-button:hover {
    opacity: 1;
}


/* Toast内容样式 */

.Toastify__toast-body {
    padding: 0;
    margin: 0;
    flex: 1;
    display: flex;
    align-items: center;
}


/* 默认Toast - 浅绿色 */

.Toastify__toast--default {
    background: linear-gradient(90deg, #e8f5e8 0%, #f0f9f0 50%, #ffffff 100%);
    color: #2d5a2d;
}


/* 成功Toast - 绿色 */

.Toastify__toast--success {
    background: linear-gradient(90deg, #d9f7be 0%, #f6ffed 50%, #ffffff 100%);
    color: #135200;
}

.Toastify__toast-icon {
    width: 18px;
}


/* 错误Toast - 红色 */

.Toastify__toast--error {
    background: linear-gradient(90deg, #ffe6e6 0%, #fff0f0 50%, #ffffff 100%);
    color: #a8071a;
}

.Toastify__progress-bar--wrp {
    opacity: 0;
    height: 0;
    overflow: hidden;
}


/* 警告Toast - 橘黄色 */

.Toastify__toast--warning {
    background: linear-gradient(90deg, #fff1b8 0%, #fffbe6 50%, #ffffff 100%);
    color: #ad6800;
}


/* 信息Toast - 浅蓝色 */

.Toastify__toast--info {
    background: linear-gradient(90deg, #bae7ff 0%, #e6f7ff 50%, #ffffff 100%);
    color: #003a8c;
}


/* 悬停效果 */

.Toastify__toast:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}


/* 进入动画 */

@keyframes toastSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes toastSlideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.Toastify__slide-enter {
    animation: toastSlideIn 0.4s ease-out;
}

.Toastify__slide-exit {
    animation: toastSlideOut 0.3s ease-in;
}


/* 响应式设计 */

@media (max-width: 768px) {
    .Toastify__toast-container {
        width: 100vw;
        padding: 0 16px;
        left: 0;
        margin: 0;
    }
    .Toastify__toast {
        margin-bottom: 8px;
        border-radius: 8px;
        min-height: 48px;
        padding: 12px 16px;
    }
    .Toastify__toast-icon {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        font-size: 12px;
    }
    .Toastify__close-button {
        width: 18px;
        height: 18px;
        top: 10px;
        right: 10px;
        font-size: 14px;
    }
}


/* 暗色主题支持 */

@media (prefers-color-scheme: dark) {
    .Toastify__toast--default {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        color: #d1fae5;
    }
    .Toastify__toast--success {
        background: linear-gradient(135deg, #064e3b 0%, #065f46 100%);
        color: #d1fae5;
    }
    .Toastify__toast--error {
        background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
        color: #fecaca;
    }
    .Toastify__toast--warning {
        background: linear-gradient(135deg, #78350f 0%, #92400e 100%);
        color: #fed7aa;
    }
    .Toastify__toast--info {
        background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
        color: #dbeafe;
    }
}