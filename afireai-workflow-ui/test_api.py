#!/usr/bin/env python3

import sys
import os
sys.path.append('/Users/<USER>/git/afireai_api')

from fastapi import FastAPI, Depends, HTTPException
from sqlalchemy.orm import Session
from database import get_db
from models.workflow_run_nodes import WorkflowRunNode
from models.workflow_runs import WorkflowRun
import uvicorn

app = FastAPI()

@app.get("/test/workflow_run_nodes/{workflow_run_id}")
async def test_get_workflow_run_nodes(
    workflow_run_id: str,
    db: Session = Depends(get_db)
):
    """测试获取工作流运行节点"""
    try:
        # 获取工作流运行节点
        workflow_run_nodes = db.query(WorkflowRunNode).filter(
            WorkflowRunNode.workflow_run_id == workflow_run_id
        ).all()
        
        # 返回节点详情
        node_details = []
        for node in workflow_run_nodes:
            node_details.append({
                "id": node.id,
                "node_id": node.node_id,
                "status": node.status,
                "started_at": node.started_at,
                "finished_at": node.finished_at,
                "output_type": node.output_type,
                "output": node.output
            })
        
        return {
            "workflow_run_id": workflow_run_id,
            "node_count": len(node_details),
            "node_details": node_details
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
